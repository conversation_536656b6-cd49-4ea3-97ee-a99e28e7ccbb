/**
 * Simple test to instantiate GreenCheckProvider
 */

console.log('🔍 Testing GreenCheckProvider instantiation...\n');

// Check environment variables first
console.log('📋 Environment Variables:');
console.log('GREEN_CHECK_CLIENT_ID:', process.env.GREEN_CHECK_CLIENT_ID ? 'SET' : 'MISSING');
console.log('GREEN_CHECK_CLIENT_SECRET:', process.env.GREEN_CHECK_CLIENT_SECRET ? 'SET' : 'MISSING');
console.log('GREEN_CHECK_SERVICE_PROVIDER_ID:', process.env.GREEN_CHECK_SERVICE_PROVIDER_ID ? 'SET' : 'MISSING');
console.log('GREEN_CHECK_BASE_URL:', process.env.GREEN_CHECK_BASE_URL || 'NOT SET');

// Try to load dotenv manually
console.log('\n🔄 Loading dotenv manually...');
try {
  require('dotenv').config({ path: '.env' });
  require('dotenv').config({ path: '/usr/src/app/.env' });
  require('dotenv').config({ path: '/usr/src/app/apps/platform/.env' });
  console.log('✅ Dotenv loaded');
} catch (error) {
  console.log('❌ Dotenv loading failed:', error.message);
}

// Check environment variables after dotenv
console.log('\n📋 Environment Variables after dotenv:');
console.log('GREEN_CHECK_CLIENT_ID:', process.env.GREEN_CHECK_CLIENT_ID ? 'SET' : 'MISSING');
console.log('GREEN_CHECK_CLIENT_SECRET:', process.env.GREEN_CHECK_CLIENT_SECRET ? 'SET' : 'MISSING');
console.log('GREEN_CHECK_SERVICE_PROVIDER_ID:', process.env.GREEN_CHECK_SERVICE_PROVIDER_ID ? 'SET' : 'MISSING');
console.log('GREEN_CHECK_BASE_URL:', process.env.GREEN_CHECK_BASE_URL || 'NOT SET');

// Try to create GreenCheckProvider
console.log('\n🏗️  Testing GreenCheckProvider creation...');
try {
  // We need to import from the compiled JavaScript, not TypeScript
  // Let's try different import paths
  let GreenCheckProvider;
  
  try {
    // Try importing from the built files
    const module = require('./build/providers/pos/GreenCheckProvider');
    GreenCheckProvider = module.GreenCheckProvider || module.default || module;
  } catch (importError) {
    console.log('❌ Could not import GreenCheckProvider:', importError.message);
    console.log('   Available files in build/providers/pos/:');
    try {
      const fs = require('fs');
      const files = fs.readdirSync('./build/providers/pos/');
      console.log('   ', files.join(', '));
    } catch (fsError) {
      console.log('   Could not list files:', fsError.message);
    }
    process.exit(1);
  }
  
  // Create provider instance
  const provider = new GreenCheckProvider({}, 1);
  console.log('✅ GreenCheckProvider created successfully');
  
  // Try to authenticate
  console.log('\n🔐 Testing authentication...');
  await provider.authenticate();
  console.log('✅ Authentication successful');
  
} catch (error) {
  console.log('❌ GreenCheckProvider test failed:', error.message);
  console.log('   Stack:', error.stack);
}
