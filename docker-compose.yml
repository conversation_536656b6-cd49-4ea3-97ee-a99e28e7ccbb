version: "3.4"
services:
  mysql:
    image: mysql:8.0.36
    platform: linux/x86_64
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=${DB_DATABASE}
    ports:
      - "3306:3306"
    expose:
      - 3306
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "mysqladmin",
          "ping",
          "-h",
          "localhost",
          "-u",
          "root",
          "-p$$MYSQL_ROOT_PASSWORD",
        ]
      interval: 5s
      timeout: 20s
      retries: 30
  redis:
    image: redis
    expose:
      - "6379"
    restart: always
    volumes:
      - redis_data:/data
  api:
    platform: linux/x86_64
    build:
      context: .
      dockerfile: apps/platform/Dockerfile
    restart: always
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost:3001/api/health || exit 1
      interval: 10s
      retries: 50
      start_period: 60s
      timeout: 10s
    ports:
      - 3001:3001
    links:
      - mysql:mysql
      - redis:redis
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    environment:
      NODE_ENV: ${NODE_ENV}
      BASE_URL: ${BASE_URL}
      RUNNER: "api"
      NODE_OPTIONS: --openssl-legacy-provider
      APP_SECRET: ${APP_SECRET}
      PORT: 3001
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_COMPILED_MESSAGE: ${LOG_COMPILED_MESSAGE:-true}
      DB_CLIENT: ${DB_CLIENT}
      DB_HOST: mysql
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_PORT: ${DB_PORT}
      DB_DATABASE: ${DB_DATABASE}
      STORAGE_DRIVER: ${STORAGE_DRIVER}
      STORAGE_BASE_URL: ${STORAGE_BASE_URL}
      STORAGE_S3_BUCKET: ${STORAGE_S3_BUCKET}
      STORAGE_S3_ENDPOINT: ${STORAGE_S3_ENDPOINT}
      STORAGE_S3_FORCE_PATH_STYLE: ${STORAGE_S3_FORCE_PATH_STYLE}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
      QUEUE_DRIVER: ${QUEUE_DRIVER}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_TLS: ${REDIS_TLS}
      AWS_SQS_QUEUE_URL: ${AWS_SQS_QUEUE_URL}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION}
      AUTH_DRIVER: ${AUTH_DRIVER}
      AUTH_BASIC_EMAIL: ${AUTH_BASIC_EMAIL}
      AUTH_BASIC_PASSWORD: ${AUTH_BASIC_PASSWORD}
      AUTH_BASIC_NAME: ${AUTH_BASIC_NAME}
      AUTH_SAML_CALLBACK_URL: ${AUTH_SAML_CALLBACK_URL}
      AUTH_SAML_ENTRY_POINT_URL: ${AUTH_SAML_ENTRY_POINT_URL}
      AUTH_SAML_ISSUER: ${AUTH_SAML_ISSUER}
      AUTH_SAML_CERT: ${AUTH_SAML_CERT}
      AUTH_SAML_IS_AUTHN_SIGNED: ${AUTH_SAML_IS_AUTHN_SIGNED}
      AUTH_SAML_NAME: ${AUTH_SAML_NAME}
      AUTH_OPENID_ISSUER_URL: ${AUTH_OPENID_ISSUER_URL}
      AUTH_OPENID_CLIENT_ID: ${AUTH_OPENID_CLIENT_ID}
      AUTH_OPENID_CLIENT_SECRET: ${AUTH_OPENID_CLIENT_SECRET}
      AUTH_OPENID_REDIRECT_URI: ${AUTH_OPENID_REDIRECT_URI}
      AUTH_OPENID_DOMAIN_WHITELIST: ${AUTH_OPENID_DOMAIN_WHITELIST}
      AUTH_OPENID_RESPONSE_TYPES: ${AUTH_OPENID_RESPONSE_TYPES}
      AUTH_OPENID_NAME: ${AUTH_OPENID_NAME}
      AUTH_GOOGLE_ISSUER_URL: ${AUTH_GOOGLE_ISSUER_URL}
      AUTH_GOOGLE_CLIENT_ID: ${AUTH_GOOGLE_CLIENT_ID}
      AUTH_GOOGLE_CLIENT_SECRET: ${AUTH_GOOGLE_CLIENT_SECRET}
      AUTH_GOOGLE_NAME: ${AUTH_GOOGLE_NAME}
      ERROR_DRIVER: ${ERROR_DRIVER}
      ERROR_BUGSNAG_API_KEY: ${ERROR_BUGSNAG_API_KEY}
      ERROR_SENTRY_DSN: ${ERROR_SENTRY_DSN}
      TRACKING_LINK_WRAP: ${TRACKING_LINK_WRAP}
      TRACKING_DEEPLINK_MIRROR_URL: ${TRACKING_DEEPLINK_MIRROR_URL}
      AUTH_FIREBASE_CREDENTIALS: ${AUTH_FIREBASE_CREDENTIALS}
      AUTH_FIREBASE_NAME: ${AUTH_FIREBASE_NAME}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      PINECONE_API_KEY: ${PINECONE_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      FIREBASE_API_KEY: ${REACT_APP_FIREBASE_API_KEY}
      FIREBASE_PRIVATE_KEY: "${FIREBASE_PRIVATE_KEY}"
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}
      FIREBASE_AUTH_DOMAIN: ${FIREBASE_AUTH_DOMAIN}
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_STORAGE_BUCKET: ${FIREBASE_STORAGE_BUCKET}
      FIREBASE_MESSAGING_SENDER_ID: ${FIREBASE_MESSAGING_SENDER_ID}
      FIREBASE_APP_ID: ${FIREBASE_APP_ID}
      FIREBASE_MEASUREMENT_ID: ${FIREBASE_MEASUREMENT_ID}
      REACT_APP_RECAPTCHA_SITE_KEY: ${REACT_APP_RECAPTCHA_SITE_KEY}
      GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_KEY: ${SUPABASE_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      SUPABASE_BUCKET: ${SUPABASE_BUCKET}
      STRIPE_ADD_ON_PRICE_ID: ${STRIPE_ADD_ON_PRICE_ID}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      STRIPE_ENDPOINT_SECRET: ${STRIPE_ENDPOINT_SECRET}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      OAUTH_LINKEDIN_REDIRECT_URI: ${OAUTH_LINKEDIN_REDIRECT_URI}
      OAUTH_LINKEDIN_CLIENT_ID: ${OAUTH_LINKEDIN_CLIENT_ID}
      OAUTH_LINKEDIN_CLIENT_SECRET: ${OAUTH_LINKEDIN_CLIENT_SECRET}
      SUPABASE_TOKEN: ${SUPABASE_TOKEN}
      SUPABASE_WORKSPACE_ID: ${SUPABASE_WORKSPACE_ID}
      SUPABASE_API_TOKEN: ${SUPABASE_API_TOKEN}
      SUPABASE_SQL_WAREHOUSE_ID: ${SUPABASE_SQL_WAREHOUSE_ID}
      APP_CHECK_SECRET: ${APP_CHECK_SECRET}
      IDEOGRAM_API_KEY: ${IDEOGRAM_API_KEY}
      # Green Check POS Integration
      GREEN_CHECK_CLIENT_ID: ${GREEN_CHECK_CLIENT_ID}
      GREEN_CHECK_CLIENT_SECRET: ${GREEN_CHECK_CLIENT_SECRET}
      GREEN_CHECK_SERVICE_PROVIDER_ID: ${GREEN_CHECK_SERVICE_PROVIDER_ID}
      GREEN_CHECK_BASE_URL: ${GREEN_CHECK_BASE_URL}
      GREEN_CHECK_ENVIRONMENT: ${GREEN_CHECK_ENVIRONMENT}
    volumes:
      - uploads:/usr/src/app/public/uploads
      - ./firebase-credentials.json:/usr/src/app/credentials/firebase-credentials.json:ro
  worker:
    platform: linux/x86_64
    build:
      context: .
      dockerfile: apps/platform/Dockerfile
    restart: always
    links:
      - mysql:mysql
      - redis:redis
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
      api:
        condition: service_healthy
    environment:
      NODE_ENV: ${NODE_ENV}
      BASE_URL: ${BASE_URL}
      NODE_OPTIONS: --openssl-legacy-provider
      RUNNER: "worker"
      APP_SECRET: ${APP_SECRET}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_COMPILED_MESSAGE: ${LOG_COMPILED_MESSAGE:-true}
      DB_CLIENT: ${DB_CLIENT}
      DB_HOST: mysql
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_PORT: ${DB_PORT}
      DB_DATABASE: ${DB_DATABASE}
      STORAGE_DRIVER: ${STORAGE_DRIVER}
      STORAGE_BASE_URL: ${STORAGE_BASE_URL}
      STORAGE_S3_BUCKET: ${STORAGE_S3_BUCKET}
      STORAGE_S3_ENDPOINT: ${STORAGE_S3_ENDPOINT}
      STORAGE_S3_FORCE_PATH_STYLE: ${STORAGE_S3_FORCE_PATH_STYLE}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
      QUEUE_DRIVER: ${QUEUE_DRIVER}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_TLS: ${REDIS_TLS}
      AWS_SQS_QUEUE_URL: ${AWS_SQS_QUEUE_URL}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION}
      AUTH_DRIVER: ${AUTH_DRIVER}
      AUTH_BASIC_EMAIL: ${AUTH_BASIC_EMAIL}
      AUTH_BASIC_PASSWORD: ${AUTH_BASIC_PASSWORD}
      AUTH_BASIC_NAME: ${AUTH_BASIC_NAME}
      AUTH_SAML_CALLBACK_URL: ${AUTH_SAML_CALLBACK_URL}
      AUTH_SAML_ENTRY_POINT_URL: ${AUTH_SAML_ENTRY_POINT_URL}
      AUTH_SAML_ISSUER: ${AUTH_SAML_ISSUER}
      AUTH_SAML_CERT: ${AUTH_SAML_CERT}
      AUTH_SAML_IS_AUTHN_SIGNED: ${AUTH_SAML_IS_AUTHN_SIGNED}
      AUTH_SAML_NAME: ${AUTH_SAML_NAME}
      AUTH_OPENID_ISSUER_URL: ${AUTH_OPENID_ISSUER_URL}
      AUTH_OPENID_CLIENT_ID: ${AUTH_OPENID_CLIENT_ID}
      AUTH_OPENID_CLIENT_SECRET: ${AUTH_OPENID_CLIENT_SECRET}
      AUTH_OPENID_REDIRECT_URI: ${AUTH_OPENID_REDIRECT_URI}
      AUTH_OPENID_DOMAIN_WHITELIST: ${AUTH_OPENID_DOMAIN_WHITELIST}
      AUTH_OPENID_NAME: ${AUTH_OPENID_NAME}
      AUTH_GOOGLE_ISSUER_URL: ${AUTH_GOOGLE_ISSUER_URL}
      AUTH_GOOGLE_CLIENT_ID: ${AUTH_GOOGLE_CLIENT_ID}
      AUTH_GOOGLE_CLIENT_SECRET: ${AUTH_GOOGLE_CLIENT_SECRET}
      AUTH_GOOGLE_NAME: ${AUTH_GOOGLE_NAME}
      ERROR_DRIVER: ${ERROR_DRIVER}
      ERROR_BUGSNAG_API_KEY: ${ERROR_BUGSNAG_API_KEY}
      ERROR_SENTRY_DSN: ${ERROR_SENTRY_DSN}
      TRACKING_LINK_WRAP: ${TRACKING_LINK_WRAP}
      TRACKING_DEEPLINK_MIRROR_URL: ${TRACKING_DEEPLINK_MIRROR_URL}
      AUTH_FIREBASE_CREDENTIALS: ${AUTH_FIREBASE_CREDENTIALS}
      AUTH_FIREBASE_NAME: ${AUTH_FIREBASE_NAME}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      PINECONE_API_KEY: ${PINECONE_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      FIREBASE_API_KEY: ${FIREBASE_API_KEY}
      FIREBASE_AUTH_DOMAIN: ${FIREBASE_AUTH_DOMAIN}
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_STORAGE_BUCKET: ${FIREBASE_STORAGE_BUCKET}
      FIREBASE_MESSAGING_SENDER_ID: ${FIREBASE_MESSAGING_SENDER_ID}
      FIREBASE_APP_ID: ${FIREBASE_APP_ID}
      FIREBASE_MEASUREMENT_ID: ${FIREBASE_MEASUREMENT_ID}
      FIREBASE_PRIVATE_KEY: "${FIREBASE_PRIVATE_KEY}"
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}
      REACT_APP_RECAPTCHA_SITE_KEY: ${REACT_APP_RECAPTCHA_SITE_KEY}
      GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_ADD_ON_PRICE_ID: ${STRIPE_ADD_ON_PRICE_ID}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      STRIPE_ENDPOINT_SECRET: ${STRIPE_ENDPOINT_SECRET}
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_KEY: ${SUPABASE_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      SUPABASE_BUCKET: ${SUPABASE_BUCKET}
      SUPABASE_TOKEN: ${SUPABASE_TOKEN}
      SUPABASE_WORKSPACE_ID: ${SUPABASE_WORKSPACE_ID}
      SUPABASE_API_TOKEN: ${SUPABASE_API_TOKEN}
      SUPABASE_SQL_WAREHOUSE_ID: ${SUPABASE_SQL_WAREHOUSE_ID}
      APP_CHECK_SECRET: ${APP_CHECK_SECRET}
      IDEOGRAM_API_KEY: ${IDEOGRAM_API_KEY}
      # Green Check POS Integration
      GREEN_CHECK_CLIENT_ID: ${GREEN_CHECK_CLIENT_ID}
      GREEN_CHECK_CLIENT_SECRET: ${GREEN_CHECK_CLIENT_SECRET}
      GREEN_CHECK_SERVICE_PROVIDER_ID: ${GREEN_CHECK_SERVICE_PROVIDER_ID}
      GREEN_CHECK_BASE_URL: ${GREEN_CHECK_BASE_URL}
      GREEN_CHECK_ENVIRONMENT: ${GREEN_CHECK_ENVIRONMENT}
    volumes:
      - uploads:/usr/src/app/public/uploads
      - ./firebase-credentials.json:/usr/src/app/credentials/firebase-credentials.json:ro
  ui:
    platform: linux/x86_64
    build:
      context: .
      dockerfile: apps/ui/Dockerfile
    depends_on:
      api:
        condition: service_healthy
    environment:
      API_BASE_URL: ${API_BASE_URL}
      REACT_APP_FIREBASE_CONFIG: '{"apiKey":"${REACT_APP_FIREBASE_API_KEY}","authDomain":"${FIREBASE_AUTH_DOMAIN}","projectId":"${FIREBASE_PROJECT_ID}","storageBucket":"${FIREBASE_STORAGE_BUCKET}","messagingSenderId":"${FIREBASE_MESSAGING_SENDER_ID}","appId":"${FIREBASE_APP_ID}","measurementId":"${FIREBASE_MEASUREMENT_ID}"}'
      REACT_APP_FIREBASE_API_KEY: ${REACT_APP_FIREBASE_API_KEY}
      REACT_APP_FIREBASE_AUTH_DOMAIN: ${REACT_APP_FIREBASE_AUTH_DOMAIN}
      REACT_APP_FIREBASE_PROJECT_ID: ${REACT_APP_FIREBASE_PROJECT_ID}
      REACT_APP_FIREBASE_STORAGE_BUCKET: ${REACT_APP_FIREBASE_STORAGE_BUCKET}
      REACT_APP_FIREBASE_MESSAGING_SENDER_ID: ${REACT_APP_FIREBASE_MESSAGING_SENDER_ID}
      REACT_APP_FIREBASE_APP_ID: ${REACT_APP_FIREBASE_APP_ID}
      REACT_APP_FIREBASE_MEASUREMENT_ID: ${REACT_APP_FIREBASE_MEASUREMENT_ID}
      REACT_APP_AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      REACT_APP_AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      REACT_APP_AWS_REGION: ${AWS_REGION}
      REACT_APP_RECAPTCHA_SITE_KEY: ${REACT_APP_RECAPTCHA_SITE_KEY}
      REACT_APP_GOOGLE_MAPS_API_KEY: ${REACT_APP_GOOGLE_MAPS_API_KEY}
      REACT_APP_STRIPE_PUBLISHABLE_KEY: ${REACT_APP_STRIPE_PUBLISHABLE_KEY}
      REACT_APP_STRIPE_PRICING_TABLE_ID: ${REACT_APP_STRIPE_PRICING_TABLE_ID}
      REACT_APP_API_BASE_URL: "/api"
      REACT_APP_PROXY_URL: "http://api:3001"
      REACT_APP_AUTH_CALLBACK_URL: ${AUTH_SAML_CALLBACK_URL}
      REACT_APP_FACEBOOK_APP_ID: ${REACT_APP_FACEBOOK_APP_ID}
      REACT_APP_LINKEDIN_CLIENT_ID: ${REACT_APP_LINKEDIN_CLIENT_ID}
      REACT_APP_SUBSCRIPTION_COMPLETED_URL: ${REACT_APP_SUBSCRIPTION_COMPLETED_URL}
      APP_CHECK_SECRET: ${APP_CHECK_SECRET}

      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_KEY: ${SUPABASE_KEY}
      SUPABASE_BUCKET: ${SUPABASE_BUCKET}
      SUPABASE_TOKEN: ${SUPABASE_TOKEN}
      SUPABASE_WORKSPACE_ID: ${SUPABASE_WORKSPACE_ID}
      SUPABASE_API_TOKEN: ${SUPABASE_API_TOKEN}
      SUPABASE_SQL_WAREHOUSE_ID: ${SUPABASE_SQL_WAREHOUSE_ID}
    ports:
      - ${UI_PORT}:3000
  # rag-python:
  #   build:
  #     context: .
  #     dockerfile: docker/rag-python.Dockerfile
  #   restart: always
  #   ports:
  #     - "8000:8000"
  #   depends_on:
  #     api:
  #       condition: service_healthy
  #   environment:
  #     OPENAI_API_KEY: ${OPENAI_API_KEY}
  #     PINECONE_API_KEY: ${PINECONE_API_KEY}
  #     MYSQL_HOST: mysql
  #     MYSQL_DATABASE: ${DB_DATABASE}
  #     MYSQL_USER: ${DB_USERNAME}
  #     MYSQL_PASSWORD: ${DB_PASSWORD}
  #     SUPABASE_URL: ${SUPABASE_URL}
  #     SUPABASE_KEY: ${SUPABASE_KEY}
  #     SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  uploads:
    driver: local
