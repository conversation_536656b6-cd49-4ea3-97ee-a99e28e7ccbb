#!/bin/bash

echo "🔍 Testing Environment Variable Loading..."
echo ""

echo "📋 Checking if variables are available to Docker Compose:"
echo "GREEN_CHECK_CLIENT_ID: ${GREEN_CHECK_CLIENT_ID:-NOT_SET}"
echo "GREEN_CHECK_CLIENT_SECRET: ${GREEN_CHECK_CLIENT_SECRET:-NOT_SET}"
echo "GREEN_CHECK_SERVICE_PROVIDER_ID: ${GREEN_CHECK_SERVICE_PROVIDER_ID:-NOT_SET}"
echo "GREEN_CHECK_BASE_URL: ${GREEN_CHECK_BASE_URL:-NOT_SET}"
echo "GREEN_CHECK_ENVIRONMENT: ${GREEN_CHECK_ENVIRONMENT:-NOT_SET}"

echo ""
echo "📁 Checking .env file contents:"
if [ -f ".env" ]; then
    echo "✅ .env file exists"
    if grep -q "GREEN_CHECK_CLIENT_ID" .env; then
        echo "✅ GREEN_CHECK_CLIENT_ID found in .env"
    else
        echo "❌ GREEN_CHECK_CLIENT_ID not found in .env"
    fi
else
    echo "❌ .env file not found"
fi

echo ""
echo "🔧 Loading .env manually:"
if [ -f ".env" ]; then
    source .env
    echo "GREEN_CHECK_CLIENT_ID after source: ${GREEN_CHECK_CLIENT_ID:-NOT_SET}"
    echo "GREEN_CHECK_CLIENT_SECRET after source: ${GREEN_CHECK_CLIENT_SECRET:-NOT_SET}"
    echo "GREEN_CHECK_SERVICE_PROVIDER_ID after source: ${GREEN_CHECK_SERVICE_PROVIDER_ID:-NOT_SET}"
fi
