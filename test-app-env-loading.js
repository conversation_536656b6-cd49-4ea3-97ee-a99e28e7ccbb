/**
 * Test if app.ts environment loading works
 */

console.log('🔍 Testing App.ts Environment Loading...\n');

console.log('📋 Environment Variables before importing App:');
const greenCheckVars = [
  'GREEN_CHECK_CLIENT_ID',
  'GREEN_CHECK_CLIENT_SECRET', 
  'GREEN_CHECK_SERVICE_PROVIDER_ID',
  'GREEN_CHECK_BASE_URL',
  'GREEN_CHECK_ENVIRONMENT'
];

greenCheckVars.forEach(varName => {
  const value = process.env[varName];
  console.log(`   ${varName}: ${value ? '✅ SET' : '❌ MISSING'}`);
});

console.log('\n🏗️  Importing App...');
try {
  // Import the App class which should load environment variables
  const { App } = require('./build/app');
  console.log('✅ App imported successfully');
  
  console.log('\n📋 Environment Variables after importing App:');
  greenCheckVars.forEach(varName => {
    const value = process.env[varName];
    console.log(`   ${varName}: ${value ? '✅ SET' : '❌ MISSING'}`);
    if (value && varName !== 'GREEN_CHECK_CLIENT_SECRET') {
      console.log(`      Value: ${value}`);
    }
  });
  
} catch (error) {
  console.log('❌ Failed to import App:', error.message);
  console.log('   Stack:', error.stack);
}

console.log('\n🎯 App environment loading test completed!');
