/**
 * Direct test of environment variable loading
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Direct Environment Variable Loading Test...\n');

console.log('📂 Current working directory:', process.cwd());

// Check if .env files exist
const envPaths = [
  '.env',
  '/usr/src/app/.env',
  '/usr/src/app/apps/platform/.env',
  '../../.env'
];

console.log('\n📁 Checking .env file existence:');
envPaths.forEach(envPath => {
  const exists = fs.existsSync(envPath);
  console.log(`   ${envPath}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
  
  if (exists) {
    try {
      const content = fs.readFileSync(envPath, 'utf8');
      const hasGreenCheck = content.includes('GREEN_CHECK_CLIENT_ID');
      console.log(`      Contains GREEN_CHECK vars: ${hasGreenCheck ? '✅ YES' : '❌ NO'}`);
      if (hasGreen<PERSON>heck) {
        const lines = content.split('\n').filter(line => line.includes('GREEN_CHECK'));
        console.log(`      GREEN_CHECK lines found: ${lines.length}`);
      }
    } catch (error) {
      console.log(`      Error reading file: ${error.message}`);
    }
  }
});

// Check current environment variables
console.log('\n🔧 Current Environment Variables:');
const greenCheckVars = [
  'GREEN_CHECK_CLIENT_ID',
  'GREEN_CHECK_CLIENT_SECRET', 
  'GREEN_CHECK_SERVICE_PROVIDER_ID',
  'GREEN_CHECK_BASE_URL',
  'GREEN_CHECK_ENVIRONMENT'
];

greenCheckVars.forEach(varName => {
  const value = process.env[varName];
  console.log(`   ${varName}: ${value ? '✅ SET' : '❌ MISSING'}`);
});

// Try loading dotenv manually from each path
console.log('\n🔄 Manual dotenv loading:');
envPaths.forEach(envPath => {
  if (fs.existsSync(envPath)) {
    try {
      const result = require('dotenv').config({ path: envPath });
      console.log(`   ${envPath}: ✅ LOADED`);
      if (result.error) {
        console.log(`      Error: ${result.error.message}`);
      }
    } catch (error) {
      console.log(`   ${envPath}: ❌ FAILED - ${error.message}`);
    }
  }
});

// Check environment variables after manual loading
console.log('\n🔍 Environment Variables after manual loading:');
greenCheckVars.forEach(varName => {
  const value = process.env[varName];
  console.log(`   ${varName}: ${value ? '✅ SET' : '❌ MISSING'}`);
  if (value && varName !== 'GREEN_CHECK_CLIENT_SECRET') {
    console.log(`      Value: ${value}`);
  }
});

// Test direct credential setting
console.log('\n🧪 Testing direct credential setting:');
process.env.GREEN_CHECK_CLIENT_ID = '5fad96d5-4842-4377-ad48-57f7db6b8b2c';
process.env.GREEN_CHECK_CLIENT_SECRET = 'e5ec351d29eff5d0764d5f64d0619604e2e9042447c51d0880d1b58857ead3924bdd8de654456055f5488b0576ba955fb8f7d8f4398df6c9aa621324ccda1a83cb673856b6f0b7b39c24dde770f10cf31a121709772576ba29c517f5d16aabf5d873f1fd291f1959870429cd72bfecab4c63fccd106caaf2ee50dfd2cd20312d';
process.env.GREEN_CHECK_SERVICE_PROVIDER_ID = 'f2b48e57-7114-432d-8f11-a0d74b8fb934';
process.env.GREEN_CHECK_BASE_URL = 'https://sandbox-api.greencheckverified.com';
process.env.GREEN_CHECK_ENVIRONMENT = 'sandbox';

console.log('✅ Credentials set directly in process.env');

// Verify they're set
greenCheckVars.forEach(varName => {
  const value = process.env[varName];
  console.log(`   ${varName}: ${value ? '✅ SET' : '❌ MISSING'}`);
});

console.log('\n🎯 Environment variable loading test completed!');
