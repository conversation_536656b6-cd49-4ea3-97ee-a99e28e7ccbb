/**
 * Test Green Check Provider with hardcoded credentials
 */

console.log('🔍 Testing Green Check Provider with Hardcoded Credentials...\n');

try {
  // Import the GreenCheckProvider
  const { GreenCheckProvider } = require('./build/providers/pos/GreenCheckProvider');
  
  console.log('✅ GreenCheckProvider imported successfully');
  
  // Create a provider instance with empty config (should use hardcoded values)
  const provider = new GreenCheckProvider({}, 1);
  
  console.log('✅ GreenCheckProvider instance created');
  
  // Test authentication
  console.log('\n🔐 Testing authentication...');
  
  provider.authenticate()
    .then(() => {
      console.log('✅ Authentication successful!');
      
      // Test getting CRBs
      console.log('\n🏢 Testing getCRBs...');
      return provider.getCRBs();
    })
    .then((crbs) => {
      console.log('✅ getCRBs successful!');
      console.log(`   Found ${crbs.length} CRBs`);
      if (crbs.length > 0) {
        console.log(`   First CRB: ${crbs[0].name} (ID: ${crbs[0].id})`);
      }
    })
    .catch((error) => {
      console.log('❌ Error:', error.message);
      console.log('   Stack:', error.stack);
    });
    
} catch (error) {
  console.log('❌ Failed to import or create GreenCheckProvider:', error.message);
  console.log('   Stack:', error.stack);
}
