/**
 * Debug script to test Green Check integration within the app context
 */

// Load environment variables manually first
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '/usr/src/app/.env' });
require('dotenv').config({ path: '/usr/src/app/apps/platform/.env' });

const axios = require('axios');

async function debugGreenCheckInApp() {
  console.log('🔍 Debugging Green Check Integration in App Context...\n');

  try {
    // Test 1: Direct environment variable access
    console.log('📋 Step 1: Environment Variables');
    const clientId = process.env.GREEN_CHECK_CLIENT_ID;
    const clientSecret = process.env.GREEN_CHECK_CLIENT_SECRET;
    const serviceProviderId = process.env.GREEN_CHECK_SERVICE_PROVIDER_ID;
    const baseUrl = process.env.GREEN_CHECK_BASE_URL || 'https://sandbox-api.greencheckverified.com';

    console.log(`   CLIENT_ID: ${clientId ? '✅ Set' : '❌ Missing'}`);
    console.log(`   CLIENT_SECRET: ${clientSecret ? '✅ Set' : '❌ Missing'}`);
    console.log(`   SERVICE_PROVIDER_ID: ${serviceProviderId ? '✅ Set' : '❌ Missing'}`);
    console.log(`   BASE_URL: ${baseUrl}`);

    // Test 2: Direct API call using environment variables
    console.log('\n🌐 Step 2: Direct API Authentication Test');
    const authUrl = `${baseUrl}/auth/token`;
    console.log(`   Auth URL: ${authUrl}`);

    try {
      const response = await axios.post(authUrl, {
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: "client_credentials",
        scope: ["service-provider:read", "point-of-sale:read"]
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log('✅ Direct API authentication successful!');
      console.log(`   Status: ${response.status}`);
      console.log(`   Token received: ${response.data.access_token ? 'Yes' : 'No'}`);

      // Test 3: Create and test GreenCheckProvider (without App.main dependency)
      console.log('\n🔧 Step 3: Testing GreenCheckProvider');

      let GreenCheckProvider;
      try {
        // Try different import methods
        GreenCheckProvider = require('./build/providers/pos/GreenCheckProvider.js').GreenCheckProvider;
      } catch (e1) {
        try {
          GreenCheckProvider = require('./build/providers/pos/GreenCheckProvider').GreenCheckProvider;
        } catch (e2) {
          try {
            const module = require('./build/providers/pos/GreenCheckProvider');
            GreenCheckProvider = module.default || module.GreenCheckProvider || module;
          } catch (e3) {
            console.log('❌ All import attempts failed:');
            console.log('   Attempt 1:', e1.message);
            console.log('   Attempt 2:', e2.message);
            console.log('   Attempt 3:', e3.message);

            // List available files
            const fs = require('fs');
            try {
              console.log('\n📁 Available files in build/providers/pos/:');
              const files = fs.readdirSync('./build/providers/pos/');
              files.forEach(file => console.log(`   ${file}`));
            } catch (fsError) {
              console.log('   Could not list files:', fsError.message);
            }

            throw new Error('Could not import GreenCheckProvider');
          }
        }
      }

      const testConfig = {
        crbId: '9439dbd7-d429-4508-a7c9-c74b22a4a1f1', // BioTrack staging data
      };

      const provider = new GreenCheckProvider(testConfig, 1);
      console.log('✅ Provider created successfully');

      // Boot the provider
      provider.boot();
      console.log('✅ Provider booted successfully');

      // Test verification
      console.log('\n🔍 Step 4: Testing Provider Verification');
      const isVerified = await provider.verify();

      if (isVerified) {
        console.log('✅ Provider verification successful!');
      } else {
        console.log('❌ Provider verification failed');
      }

    } catch (apiError) {
      console.log('❌ Direct API authentication failed!');
      console.log(`   Status: ${apiError.response?.status}`);
      console.log(`   Error: ${apiError.response?.data?.message || apiError.message}`);
      
      if (apiError.response?.data) {
        console.log(`   Response:`, JSON.stringify(apiError.response.data, null, 2));
      }
    }

  } catch (error) {
    console.error('❌ Debug script failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the debug
debugGreenCheckInApp().catch(console.error);
