/**
 * Simple environment variable test
 */

console.log('🔍 Simple Environment Variable Test\n');

console.log('📋 Raw process.env check:');
console.log('GREEN_CHECK_CLIENT_ID:', process.env.GREEN_CHECK_CLIENT_ID || 'MISSING');
console.log('GREEN_CHECK_CLIENT_SECRET:', process.env.GREEN_CHECK_CLIENT_SECRET ? 'SET' : 'MISSING');
console.log('GREEN_CHECK_SERVICE_PROVIDER_ID:', process.env.GREEN_CHECK_SERVICE_PROVIDER_ID || 'MISSING');
console.log('GREEN_CHECK_BASE_URL:', process.env.GREEN_CHECK_BASE_URL || 'MISSING');
console.log('GREEN_CHECK_ENVIRONMENT:', process.env.GREEN_CHECK_ENVIRONMENT || 'MISSING');

console.log('\n📋 Other known variables:');
console.log('NODE_ENV:', process.env.NODE_ENV || 'MISSING');
console.log('PORT:', process.env.PORT || 'MISSING');
console.log('DB_HOST:', process.env.DB_HOST || 'MISSING');

console.log('\n🔍 All environment variables containing GREEN_CHECK:');
Object.keys(process.env)
  .filter(key => key.includes('GREEN_CHECK'))
  .forEach(key => {
    const value = process.env[key];
    console.log(`${key}: ${key.includes('SECRET') ? '[REDACTED]' : value}`);
  });

console.log('\n📊 Total environment variables:', Object.keys(process.env).length);
