NODE_ENV=development
PORT=3001
APP_SECRET=
LOG_LEVEL=info

DB_HOST=127.0.0.1
DB_USERNAME=bakedBot
DB_PASSWORD=
DB_PORT=3306
DB_DATABASE=bakedBot

QUEUE_DRIVER=redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379

STORAGE_DRIVER=local
STORAGE_BASE_URL=http://localhost:3001/uploads

AUTH_DRIVER=basic
AUTH_BASIC_EMAIL=<EMAIL>
AUTH_BASIC_PASSWORD=password
AUTH_BASIC_NAME=Login


OAUTH_LINKEDIN_REDIRECT_URI=
OAUTH_LINKEDIN_CLIENT_ID=
OAUTH_LINKEDIN_CLIENT_SECRET=

# Pinecone Assistant
PINECONE_API_KEY=your_pinecone_api_key

# Green Check POS Integration
GREEN_CHECK_CLIENT_ID=your_green_check_client_id
GREEN_CHECK_CLIENT_SECRET=your_green_check_client_secret
GREEN_CHECK_SERVICE_PROVIDER_ID=your_service_provider_id
GREEN_CHECK_BASE_URL=https://sandbox-api.greencheckverified.com
GREEN_CHECK_ENVIRONMENT=sandbox