/**
 * Environment variable loader - ensures variables are loaded before app starts
 */

const dotenv = require('dotenv');
const fs = require('fs');

console.log('🔧 Loading environment variables...');

// Try to load from multiple locations
const envPaths = [
  '.env',
  '/usr/src/app/.env',
  '/usr/src/app/apps/platform/.env'
];

let loaded = false;

envPaths.forEach(path => {
  if (fs.existsSync(path)) {
    try {
      const result = dotenv.config({ path });
      if (!result.error) {
        console.log(`✅ Loaded environment from: ${path}`);
        loaded = true;
      } else {
        console.log(`❌ Error loading ${path}:`, result.error.message);
      }
    } catch (error) {
      console.log(`❌ Failed to load ${path}:`, error.message);
    }
  } else {
    console.log(`⚠️  File not found: ${path}`);
  }
});

// Verify Green Check variables are loaded
const greenCheckVars = [
  'GREEN_CHECK_CLIENT_ID',
  'GREEN_CHECK_CLIENT_SECRET', 
  'GREEN_CHECK_SERVICE_PROVIDER_ID',
  'GREEN_CHECK_BASE_URL',
  'GREEN_CHECK_ENVIRONMENT'
];

console.log('\n📋 Green Check Environment Variables:');
let allSet = true;
greenCheckVars.forEach(varName => {
  const value = process.env[varName];
  const isSet = !!value;
  console.log(`   ${varName}: ${isSet ? '✅ SET' : '❌ MISSING'}`);
  if (!isSet) allSet = false;
});

if (allSet) {
  console.log('\n🎉 All Green Check environment variables loaded successfully!');
} else {
  console.log('\n⚠️  Some Green Check environment variables are missing.');
}

module.exports = { loaded, allSet };
