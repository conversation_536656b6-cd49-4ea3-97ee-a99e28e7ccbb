# get auth token
curl --location 'https://sandbox-api.greencheckverified.com/auth/token' \
--header 'Authorization: application/json' \
--header 'Content-Type: application/json' \
--data '{
  "client_id": "5fad96d5-4842-4377-ad48-57f7db6b8b2c",
  "client_secret": "e5ec351d29eff5d0764d5f64d0619604e2e9042447c51d0880d1b58857ead3924bdd8de654456055f5488b0576ba955fb8f7d8f4398df6c9aa621324ccda1a83cb673856b6f0b7b39c24dde770f10cf31a121709772576ba29c517f5d16aabf5d873f1fd291f1959870429cd72bfecab4c63fccd106caaf2ee50dfd2cd20312d",
  "grant_type": "client_credentials",
  "scope": [
    "service-provider:read", "point-of-sale:read"
  ]
}'

# get cannabis-related businesses (crbs)
curl --location 'https://sandbox-api.greencheckverified.com/service-providers/f2b48e57-7114-432d-8f11-a0d74b8fb934/crbs' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ppcnrDQC4Ve_GwBY4cicBGFaaQVY1kJ7eStsoJoNXhI '

# get inventory on a given crb id
curl --location 'https://sandbox-api.greencheckverified.com/service-providers/f2b48e57-7114-432d-8f11-a0d74b8fb934/crbs/9439dbd7-d429-4508-a7c9-c74b22a4a1f1/inventory' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ppcnrDQC4Ve_GwBY4cicBGFaaQVY1kJ7eStsoJoNXhI'

# get sales on a given crb id
curl --location 'https://sandbox-api.greencheckverified.com/service-providers/f2b48e57-7114-432d-8f11-a0d74b8fb934/crbs/9439dbd7-d429-4508-a7c9-c74b22a4a1f1/sales?start_date=2024-01-01&end_date=2025-07-01&limit=100&offset=0' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ppcnrDQC4Ve_GwBY4cicBGFaaQVY1kJ7eStsoJoNXhI'