// Load environment variables FIRST, before any other imports
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '/usr/src/app/.env' });
require('dotenv').config({ path: '/usr/src/app/apps/platform/.env' });

import App from "./app";
import env from "./config/env";
import { logger } from "./config/logger";

// Create an async main function as the entry point
async function main() {
  try {
    const app = await App.init(env());
    await app.start();
  } catch (error) {
    console.error("Failed to start application:", error);
    process.exit(1);
  }
}

// Execute the main function
main();

// Export for testing purposes
export { main };
