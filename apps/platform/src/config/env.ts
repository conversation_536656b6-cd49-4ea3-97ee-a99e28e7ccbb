/* eslint-disable indent */
import * as dotenv from "dotenv";
import { logger } from "./logger";
import type { StorageConfig } from "../storage/Storage";
import type { QueueConfig } from "../queue/Queue";
import type { DatabaseConfig } from "./database";
import type { AuthConfig, AuthProviderName } from "../auth/Auth";
import type { ErrorConfig } from "../error/ErrorHandler";
import { RedisConfig } from "./redis";
import { isValidUrl } from "../utilities";
import * as path from "path";
import * as admin from "firebase-admin";

export type Runner = "api" | "worker";
export interface Env {
  runners: Runner[];
  config: {
    monoDocker: boolean;
    multiOrg: boolean;
    logCompiledMessage: boolean;
  };
  db: DatabaseConfig;
  queue: QueueConfig;
  storage: StorageConfig;
  baseUrl: string;
  apiBaseUrl: string;
  port: number;
  secret: string;
  auth: AuthConfig;
  error: ErrorConfig;
  redis: RedisConfig;
  openai: {
    apiKey: string;
    model?: string;
    temperature?: string;
    maxTokens?: string;
    contextWindow?: string;
  };
  productAI: {
    enabled: boolean;
    model: string;
    batchSize: number;
    fastModeThreshold: number;
    concurrentRequests: number;
  };
  supabase: {
    url: string;
    key: string;
    accessToken: string;
    projectRef: string;
    timeout?: number;
  };
  greenCheck: {
    clientId: string;
    clientSecret: string;
    serviceProviderId: string;
    baseUrl: string;
    environment: "sandbox" | "production";
  };
}

export interface DriverConfig {
  driver: string;
}

type DriverLoaders<T> = Record<string, () => T>;
const driver = <T extends DriverConfig>(
  driver: string | undefined,
  loaders: DriverLoaders<Omit<T, "driver">>
) => {
  const driverKey = driver ?? "logger";
  const loadedDriver = loaders[driverKey] ? loaders[driverKey]() : {};
  return { ...loadedDriver, driver: driverKey } as T;
};

// 24 hours?
const defaultTokenLife = 24 * 60 * 60;

type EnvType = "production" | "test";
export default (type?: EnvType): Env => {
  // Try to load .env from current directory first, then from root directory
  dotenv.config({ path: `.env${type === "test" ? ".test" : ""}` });
  dotenv.config({ path: `../../.env${type === "test" ? ".test" : ""}` });

  const port = parseInt(process.env.PORT ?? "3000");
  const baseUrl = process.env.BASE_URL ?? `http://localhost:${port}`;
  const apiBaseUrl = process.env.API_BASE_URL ?? `${baseUrl}/api`;
  const credentialsPath = path.join(
    process.cwd(),
    "credentials",
    "firebase-credentials.json"
  );

  // Validate required env vars
  if (!isValidUrl(baseUrl)) {
    logger.error(
      `bakedBot:env Please ensure BASE_URL is a valid non relative URL. Current value is "${baseUrl}"`
    );
  }

  return {
    runners: (process.env.RUNNER ?? "api,worker").split(",") as Runner[],
    config: {
      monoDocker: (process.env.MONO ?? "false") === "true",
      multiOrg: (process.env.MULTI_ORG ?? "false") === "true",
      logCompiledMessage:
        (process.env.LOG_COMPILED_MESSAGE ?? "false") === "true",
    },
    db: {
      client: process.env.DB_CLIENT || "mysql2",
      host: process.env.DB_HOST,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      port: process.env.DB_PORT ? parseInt(process.env.DB_PORT) : undefined,
      database: process.env.DB_DATABASE,
      filename: process.env.DB_FILENAME,
      migrationPaths: process.env.DB_MIGRATION_PATHS?.split(",") ?? [],
    },
    redis: {
      host: process.env.REDIS_HOST!,
      port: parseInt(process.env.REDIS_PORT!),
      tls: process.env.REDIS_TLS === "true",
    },
    queue: driver<QueueConfig>(process.env.QUEUE_DRIVER, {
      sqs: () => ({
        queueUrl: process.env.AWS_SQS_QUEUE_URL!,
        region: process.env.AWS_REGION!,
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        },
      }),
      redis: () => ({
        host: process.env.REDIS_HOST!,
        port: parseInt(process.env.REDIS_PORT!),
        tls: process.env.REDIS_TLS === "true",
        concurrency: parseInt(process.env.REDIS_CONCURRENCY ?? "10"),
        stalledInterval: parseInt(process.env.REDIS_STALLED_INTERVAL ?? "45"),
        maxStalledCount: parseInt(process.env.REDIS_MAX_STALLED_COUNT ?? "1"),
      }),
    }),
    storage: driver<StorageConfig>(process.env.STORAGE_DRIVER ?? "local", {
      s3: () => ({
        baseUrl: process.env.STORAGE_BASE_URL,
        bucket: process.env.STORAGE_S3_BUCKET ?? process.env.AWS_S3_BUCKET!,
        region: process.env.AWS_REGION ?? "us-east-1",
        endpoint: process.env.STORAGE_S3_ENDPOINT,
        forcePathStyle: process.env.STORAGE_S3_FORCE_PATH_STYLE !== "false",
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        },
      }),
      local: () => ({
        baseUrl: process.env.STORAGE_BASE_URL,
      }),
      firebase: () => {
        const credentials = {
          type: "service_account",
          projectId: process.env.FIREBASE_PROJECT_ID,
          privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        };

        return {
          baseUrl: process.env.STORAGE_BASE_URL,
          credentials,
          bucket: process.env.FIREBASE_STORAGE_BUCKET,
        };
      },
    }),
    baseUrl,
    apiBaseUrl,
    port,
    secret: process.env.APP_SECRET!,
    auth: {
      driver: (process.env.AUTH_DRIVER?.split(",") ?? []) as AuthProviderName[],
      tokenLife: defaultTokenLife,
      basic: {
        driver: "basic",
        name: process.env.AUTH_BASIC_NAME!,
        email: process.env.AUTH_BASIC_EMAIL!,
        password: process.env.AUTH_BASIC_PASSWORD!,
      },
      email: {
        driver: "email",
        from: process.env.AUTH_EMAIL_FROM!,
        host: process.env.AUTH_EMAIL_SMTP_HOST!,
        port: parseInt(process.env.AUTH_EMAIL_SMTP_PORT!),
        secure: process.env.AUTH_EMAIL_SMTP_SECURE === "true",
        auth: {
          user: process.env.AUTH_EMAIL_SMTP_USERNAME!,
          pass: process.env.AUTH_EMAIL_SMTP_PASSWORD!,
        },
      },
      saml: {
        driver: "saml",
        name: process.env.AUTH_SAML_NAME!,
        callbackUrl: `${apiBaseUrl}/auth/login/saml/callback`,
        entryPoint: process.env.AUTH_SAML_ENTRY_POINT_URL!,
        issuer: process.env.AUTH_SAML_ISSUER!,
        cert: process.env.AUTH_SAML_CERT!,
        wantAuthnResponseSigned:
          process.env.AUTH_SAML_IS_AUTHN_SIGNED === "true",
      },
      openid: {
        driver: "openid",
        name: process.env.AUTH_OPENID_NAME!,
        issuerUrl: process.env.AUTH_OPENID_ISSUER_URL!,
        clientId: process.env.AUTH_OPENID_CLIENT_ID!,
        clientSecret: process.env.AUTH_OPENID_CLIENT_SECRET!,
        redirectUri: `${apiBaseUrl}/auth/login/openid/callback`,
        domain: process.env.AUTH_OPENID_DOMAIN!,
        responseTypes: process.env.AUTH_OPENID_RESPONSE_TYPES?.split(",") ?? [
          "id_token",
        ],
      },
      google: {
        driver: "google",
        name: process.env.AUTH_GOOGLE_NAME!,
        clientId: process.env.AUTH_GOOGLE_CLIENT_ID!,
        clientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET!,
        redirectUri: `${apiBaseUrl}/auth/login/google/callback`,
      },
      multi: {
        driver: "multi",
        name: process.env.AUTH_MULTI_NAME!,
      },
      firebase: {
        driver: "firebase",
        name: process.env.AUTH_FIREBASE_NAME!,
        credentials: {
          type: "service_account",
          projectId: process.env.FIREBASE_PROJECT_ID!,
          privateKey: process.env.FIREBASE_PRIVATE_KEY
            ? process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n")
            : undefined,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL!,
        } as admin.ServiceAccount,
      },
    },
    error: driver<ErrorConfig>(process.env.ERROR_DRIVER, {
      bugsnag: () => ({
        apiKey: process.env.ERROR_BUGSNAG_API_KEY,
      }),
      sentry: () => ({
        dsn: process.env.ERROR_SENTRY_DSN,
      }),
    }),
    openai: {
      apiKey: process.env.OPENAI_API_KEY!,
      model: process.env.OPENAI_MODEL,
      temperature: process.env.OPENAI_TEMPERATURE,
      maxTokens: process.env.OPENAI_MAX_TOKENS,
      contextWindow: process.env.OPENAI_CONTEXT_WINDOW,
    },
    productAI: {
      enabled: process.env.PRODUCT_AI_ENABLED === "true",
      model: process.env.PRODUCT_ENHANCEMENT_MODEL || "gpt-4",
      batchSize: parseInt(process.env.PRODUCT_AI_BATCH_SIZE || "50"),
      fastModeThreshold: parseInt(
        process.env.PRODUCT_AI_FAST_MODE_THRESHOLD || "25"
      ),
      concurrentRequests: parseInt(
        process.env.PRODUCT_AI_CONCURRENT_REQUESTS || "5"
      ),
    },
    supabase: {
      url: process.env.SUPABASE_URL!,
      key: process.env.SUPABASE_KEY!,
      accessToken: process.env.SUPABASE_ACCESS_TOKEN!,
      projectRef: process.env.SUPABASE_PROJECT_REF!,
      timeout: process.env.SUPABASE_TIMEOUT
        ? parseInt(process.env.SUPABASE_TIMEOUT)
        : 30000, // Default 30 seconds
    },
    greenCheck: {
      clientId: process.env.GREEN_CHECK_CLIENT_ID!,
      clientSecret: process.env.GREEN_CHECK_CLIENT_SECRET!,
      serviceProviderId: process.env.GREEN_CHECK_SERVICE_PROVIDER_ID!,
      baseUrl: process.env.GREEN_CHECK_BASE_URL || "https://sandbox-api.greencheckverified.com",
      environment: (process.env.GREEN_CHECK_ENVIRONMENT as "sandbox" | "production") || "sandbox",
    },
  };
};
