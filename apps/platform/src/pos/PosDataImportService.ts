import { FileStream } from "../storage/FileStream";
import { RequestError } from "../core/errors";
import App from "../app";
import { User, UserParams } from "../users/User";
import UserPatchJob from "../users/UserPatchJob";
import { PosData } from "./PosData";
import PosDataVectorJob from "./PosDataVectorJob";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";
import axios from "axios";
import { logger } from "../config/logger";
import Location from "../locations/Location";
import { getUsersFromIdentity, createUser } from "../users/UserRepository";

// Provider-specific configurations
interface MarijuanaSoftwareConfig {
  webguid: string;
  subscription_key: string;
}

interface CovaConfig {
  company_id: number;
  location_id: number;
  bearer_token: string;
}

interface TreezConfig {
  api_key: string;
  // Add other Treez-specific config
}

interface FlowhubConfig {
  api_key: string;
  // Add other Flowhub-specific config
}

interface MeadowConfig {
  api_key: string;
  // Add other Meadow-specific config
}

interface GreenCheckConfig {
  crb_id: string;
  service_provider_id: string;
}

type PosProviderConfig =
  | { type: "marijuana_software"; config: MarijuanaSoftwareConfig }
  | { type: "cova"; config: CovaConfig }
  | { type: "treez"; config: TreezConfig }
  | { type: "flowhub"; config: FlowhubConfig }
  | { type: "meadow"; config: MeadowConfig }
  | { type: "greencheck"; config: GreenCheckConfig };

interface PosDataImportParams {
  location_id: number;
  stream?: FileStream;
  normalizedData?: NormalizedData;
  pos_provider?: PosProviderConfig;
  onProgress?: (count: number) => void;
  reindex?: boolean;
}

// Provider-specific API handlers
const posProviderHandlers: Record<
  PosProviderConfig["type"],
  (config: any) => Promise<any>
> = {
  greencheck: async (config: GreenCheckConfig) => {
    // For Green Check, we don't import data during onboarding - we just validate the connection
    // The actual data sync will happen through the GreenCheckProvider when needed
    logger.info(
      `Green Check integration configured with CRB ID: ${config.crb_id}`
    );
    return {
      success: true,
      message: "Green Check integration configured successfully",
      crb_id: config.crb_id,
      service_provider_id: config.service_provider_id,
    };
  },
  marijuana_software: async (config: MarijuanaSoftwareConfig) => {
    const msApi = axios.create({
      baseURL: "https://api.marijuanasoftwarellc.com/v1",
      headers: {
        "Cache-Control": "no-cache",
        "Ocp-Apim-Subscription-Key": config.subscription_key,
      },
    });

    // Get last 24 hours of tickets
    const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const endDate = new Date();

    const response = await msApi.get("/Tickets", {
      params: {
        webguid: config.webguid,
        startDate: startDate.toLocaleDateString("en-US"),
        endDate: endDate.toLocaleDateString("en-US"),
      },
    });

    return response.data;
  },

  cova: async (config: CovaConfig) => {
    const covaApi = axios.create({
      baseURL: "https://api.covasoft.net",
      headers: {
        Authorization: `Bearer ${config.bearer_token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    });

    // Get orders for last 24 hours
    const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const endDate = new Date().toISOString();

    const [ordersResponse, productsResponse] = await Promise.all([
      covaApi.get(
        `/pointofsale/Companies(${config.company_id})/Locations(${config.location_id})/CompletedOrders`,
        {
          params: {
            $filter: `Created ge datetimeoffset'${startDate}' and Created le datetimeoffset'${endDate}'`,
            $top: 100,
          },
        }
      ),
      covaApi.post(
        `/dataplatform/v1/Companies/${config.company_id}/DetailedProductData`,
        {
          LocationId: config.location_id,
          IncludeProductSkusAndUpcs: true,
          IncludeProductSpecifications: true,
          IncludeClassifications: true,
          IncludeProductAssets: true,
          IncludeAvailability: true,
          IncludePackageDetails: true,
          IncludePricing: true,
          IncludeTaxes: true,
          InStockOnly: true,
          Top: 100,
        }
      ),
    ]);

    return {
      orders: ordersResponse.data,
      products: productsResponse.data,
    };
  },

  treez: async (config: TreezConfig) => {
    throw new Error("Treez integration not implemented yet");
  },

  flowhub: async (config: FlowhubConfig) => {
    throw new Error("Flowhub integration not implemented yet");
  },

  meadow: async (config: MeadowConfig) => {
    throw new Error("Meadow integration not implemented yet");
  },
};

/**
 * Validates and normalizes POS data from an uploaded file stream
 * @param stream The uploaded file stream
 * @returns The normalized data ready for import
 */
export const validatePosFileData = async (
  stream: FileStream
): Promise<NormalizedData> => {
  if (!stream) {
    throw new RequestError("No file stream provided");
  }

  // Normalize the data from the stream
  const normalizedData = await DataNormalizationService.normalizeData(stream);

  // Verify this is POS data
  if (normalizedData.type !== "pos") {
    throw new RequestError(
      "The uploaded file appears to be customer data. Please use the customer data import endpoint."
    );
  }

  // Report any errors found during normalization
  if (normalizedData.errors.length > 0) {
    const errorMessages = normalizedData.errors.map(
      (err) => `Row ${err.row}: ${err.error}`
    );
    throw new RequestError(
      `Data validation errors:\n${errorMessages.join("\n")}`
    );
  }

  return normalizedData;
};

/**
 * Imports POS data from an integrated POS provider
 * @param location_id The location to import data for
 * @param pos_provider The POS provider configuration
 * @param reindex Whether to reindex the data in the vector database
 * @returns Result of the import operation
 */
export const importFromPOS = async (
  location_id: number,
  pos_provider: PosProviderConfig,
  reindex: boolean = true
): Promise<{ processed: number; errors: any[] }> => {
  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  // Get location to check existing POS integration
  const location = await Location.first((qb) => qb.where({ id: location_id }));
  if (!location) {
    throw new RequestError(`Location ${location_id} not found`);
  }

  // Check if location already has a different POS integration
  const existingPosConfig = location.data?.pos_integration;
  if (existingPosConfig && existingPosConfig.type !== pos_provider.type) {
    throw new RequestError(
      `Location already has a ${existingPosConfig.type} integration. Please remove it before adding a new one.`
    );
  }

  const handler = posProviderHandlers[pos_provider.type];
  if (!handler) {
    throw new RequestError(`Unsupported POS provider: ${pos_provider.type}`);
  }

  try {
    // Store POS configuration in location data
    await Location.update((qb) => qb.where({ id: location_id }), {
      data: {
        ...location.data,
        pos_integration: {
          type: pos_provider.type,
          config: pos_provider.config,
          last_sync: new Date().toISOString(),
        },
      },
    });

    const providerData = await handler(pos_provider.config);
    // Process the data based on provider type
    // This would need provider-specific normalization logic

    // Start the vector processing job after successful import
    if (reindex) {
      try {
        await PosDataVectorJob.from({
          location_id,
          batch_size: 100,
        }).queue();

        logger.info(
          `Queued POS data vectorization job for location ${location_id}`
        );
      } catch (vectorError) {
        logger.error(
          `Failed to queue POS vector job for location ${location_id}:`,
          vectorError
        );
        // Continue - don't fail the import if just the vector job fails
      }
    } else {
      logger.info(
        `Skipping vectorization for location ${location_id} as reindex=false was specified`
      );
    }

    return {
      processed: 0,
      errors: [],
    };
  } catch (error) {
    // If integration fails, remove the POS configuration
    await Location.update((qb) => qb.where({ id: location_id }), {
      data: {
        ...location.data,
        pos_integration: null,
      },
    });
    throw error;
  }
};

/**
 * Imports POS data from a normalized data structure
 * @param location_id The location to import data for
 * @param normalizedData Previously normalized POS data
 * @param onProgress Optional callback for progress updates
 * @param reindex Whether to reindex the data in the vector database
 * @returns Result of the import operation
 */
export const importFromNormalizedData = async (
  location_id: number,
  normalizedData: NormalizedData,
  onProgress?: (count: number) => void,
  reindex: boolean = true
): Promise<{ processed: number; errors: any[] }> => {
  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  // Get location
  const location = await Location.first((qb) => qb.where({ id: location_id }));
  if (!location) {
    throw new RequestError(`Location ${location_id} not found`);
  }

  if (normalizedData.type !== "pos") {
    throw new RequestError("Invalid data type. Expected POS data.");
  }

  try {
    // Process the data rows
    let rowCount = 0;
    const errors: any[] = [];

    for (const row of normalizedData.data) {
      rowCount++;
      const posRow = row as any; // Cast to any to access POS properties
      try {
        // Extract customer data if needed
        if (posRow.customer_name) {
          const userParams: UserParams = {
            external_id: posRow.customer_name, // Using customer name as external_id
            email: posRow.email,
            phone: posRow.phone,
            data: {
              full_name: posRow.customer_name,
              birth_date: posRow.birth_date,
              customer_type: posRow.customer_type,
            },
          };

          // Process user data if needed
          // Similar to the existing code in importFromFile
        }

        // Add location_id and remove customer PII from POS data
        const posData: Partial<PosData> = {
          location_id,
          location_name: posRow.location_name,
          master_category: posRow.master_category,
          order_date: posRow.order_date,
          customer_type: posRow.customer_type,
          budtender_name: posRow.budtender_name,
          gross_sales: posRow.gross_sales,
          returned_amount: posRow.returned_amount,
          discounted_amount: posRow.discounted_amount,
          loyalty_as_discount: posRow.loyalty_as_discount,
          net_sales: posRow.net_sales,
          inventory_cost: posRow.inventory_cost,
          inventory_profit: posRow.inventory_profit,
          loyalty_as_payment: posRow.loyalty_as_payment,
          tax_amount: posRow.tax_amount,
          invoice_total: posRow.invoice_total,
          amount_paid_in_cash: posRow.amount_paid_in_cash,
          amount_paid_in_debit: posRow.amount_paid_in_debit,
          product_name: posRow.product_name,
          customer_name: posRow.customer_name, // Keep only the reference to link with user
        };

        // Insert POS data into the database
        await PosData.insert(posData);

        if (onProgress && rowCount % 100 === 0) {
          onProgress(rowCount);
        }
      } catch (error) {
        logger.error(`Error processing row ${rowCount}:`, error);
        errors.push({
          row: rowCount,
          error: error instanceof Error ? error.message : String(error),
        });
        continue; // Skip failed rows instead of stopping
      }
    }

    if (onProgress) {
      onProgress(rowCount);
    }

    // Start the vector processing job if reindex is true
    if (reindex) {
      try {
        await PosDataVectorJob.from({
          location_id,
          batch_size: 100,
        }).queue();

        logger.info(
          `Queued POS data vectorization job for location ${location_id}`
        );
      } catch (vectorError) {
        logger.error(
          `Failed to queue POS vector job for location ${location_id}:`,
          vectorError
        );
        // Continue - don't fail the import if just the vector job fails
      }
    } else {
      logger.info(
        `Skipping vectorization for location ${location_id} as reindex=false was specified`
      );
    }

    return {
      processed: rowCount,
      errors,
    };
  } catch (error) {
    logger.error("Error in POS data import:", error);
    throw error;
  }
};

/**
 * Imports POS data from a file upload
 * @param location_id The location to import data for
 * @param stream The file upload stream to process (optional if normalizedData is provided)
 * @param reindex Whether to reindex the data in the vector database
 * @param normalizedData Pre-normalized data (optional, used for direct JSON imports)
 * @param onProgress Optional callback for progress updates
 * @returns Result of the import operation
 */
export const importFromFile = async (
  location_id: number,
  stream?: FileStream,
  reindex: boolean = true,
  normalizedData?: NormalizedData,
  onProgress?: (count: number) => void
): Promise<{ processed: number; errors: any[] }> => {
  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  // Get location
  const location = await Location.first((qb) => qb.where({ id: location_id }));
  if (!location) {
    throw new RequestError(`Location ${location_id} not found`);
  }

  // If we already have normalized data (from JSON import), use that
  if (normalizedData) {
    return await importFromNormalizedData(
      location_id,
      normalizedData,
      onProgress,
      reindex
    );
  }

  // Otherwise, process from file stream
  if (!stream) {
    throw new RequestError(
      "No file stream provided and no normalized data provided"
    );
  }

  try {
    // First validate and normalize the data from the stream
    const processedData = await validatePosFileData(stream);
    console.log(
      `Normalization complete. Found ${processedData.data.length} valid records`
    );

    // Process the normalized data
    return await importFromNormalizedData(
      location_id,
      processedData,
      onProgress,
      reindex
    );
  } catch (error) {
    logger.error("Error in POS data import:", error);
    throw error;
  }
};
