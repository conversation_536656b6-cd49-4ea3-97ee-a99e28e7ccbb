import PosProvider, {
  PosTicket,
  PosTicketItem,
  PosProduct,
} from "./PosProvider";
import { PosData } from "../../pos/PosData";

import App from "../../app";
import { logger } from "../../config/logger";
import axios, { AxiosInstance } from "axios";

interface GreenCheckProviderConfig {
  crbId: string;
  clientId?: string;
  clientSecret?: string;
  serviceProviderId?: string;
  baseUrl?: string;
}

interface GreenCheckConfig {
  clientId: string;
  clientSecret: string;
  serviceProviderId: string;
  crbId: string; // This maps to our location_id
  grantType: string;
  scope: string[];
}

interface GreenCheckCRB {
  id: string;
  name: string;
  street_address: string;
  city: string;
  state: string;
  postal_code: string;
  phone_number: string;
  website?: string;
  status: string;
  business_type: string;
  org_type: string;
  pos_configs: Array<{
    name: string;
    status: string;
    updated_date: string;
  }>;
}

interface GreenCheckSale {
  id: string;
  pos_sale_id: string;
  crb_id: string;
  subtotal: number;
  total_discounts: number;
  tax_paid: number;
  total_paid: number;
  date: string;
  transaction_type: string;
  pos_name: string;
  line_items: GreenCheckLineItem[];
  // Optional fields that may not be present in all responses
  customer?: {
    customer_id: string;
    pos_customer_id: string;
    type: "rec" | "medical";
    dob: string;
    id_type: string;
    id_expiration_date: string;
  };
  employee_id?: string;
  compliance_status?: string;
  compliance_exceptions?: string[];
  compliance_warnings?: string[] | null;
  discounts?: any[];
  taxes?: any[];
  payments?: any[];
  fees?: any[];
}

interface GreenCheckLineItem {
  product_id: string | null;
  pos_product_id: string | null;
  product_name: string | null;
  num_units: number;
  price_per_unit: number;
  grams: number;
  product_type: string | null;
  cannabis_product: boolean | null;
  line_item_discounts?: any[];
  line_item_taxes?: any[];
  line_item_fees?: any[];
}

interface GreenCheckCustomer {
  crb_id: string;
  id: string;
  pos_type: string;
  pos_customer_id: string;
  first_name: string;
  last_name: string | null;
  middle_name: string | null;
  dob: string | null;
  address_1: string | null;
  address_2: string | null;
  city: string;
  state: string | null;
  zip_code: string | null;
  phone: string | null;
  cell_phone: string | null;
  email_address: string | null;
  gender: string | null;
  pos_identification_type: string;
  pos_customer_type: string | null;
  gc_customer_type: string | null;
  medical_id: string | null;
  medical_id_expiration_date: string | null;
  drivers_license_id: string;
  drivers_license_expiration_date: string | null;
  other_id: string;
  other_id_expiration_date: string;
  caregiver: boolean;
  loyalty_member: boolean;
  loyalty_points: number;
  anonymous: boolean | null;
  pos_updated_date: string;
  pos_updated_date_local: string | null;
  gc_created_date: string;
  gc_created_date_local: string;
}

interface GreenCheckProduct {
  crb_id: string;
  id: string;
  pos_product_id: string;
  pos_type: string;
  name: string;
  gc_product_category_name: string;
  pos_product_category_name: string;
  pos_product_subcategory_name: string;
  pos_strain_name: string;
  pos_brand_name: string;
  medicated: boolean | null;
  sku: string;
  description: string;
  gc_net_weight_grams: number | null;
  pos_unit_of_measure: string;
  price: number | null;
  unit_cost: number;
  pos_updated_date: string;
  pos_updated_date_local: string;
  gc_created_date: string;
  gc_created_date_local: string;
  price_tiers: any[] | null;
  images: any[] | null;
}

interface GreenCheckLocation {
  id: string;
  name: string;
  street_address: string;
  street_address_2: string;
  city: string;
  ein: string;
  phone_number: string;
  postal_code: string;
  country: string;
  state: string;
  website: string;
  timezone: string;
  mailing_street_address: string;
  mailing_city: string;
  mailing_state: string;
  mailing_postal_code: string;
  established_date: string;
  ftEmployees: number;
  ptEmployees: number;
  entityType: string;
  dba: string;
  monthlySales: string;
  monthlyCustomers: number;
  org_type: string;
  business_type: string;
  template_id: string;
  template_result_id: string;
  status: string;
  pos_configs: Array<{
    name: string;
    status: string;
    updated_date: string;
  }>;
}

export default class GreenCheckProvider extends PosProvider {
  private config: GreenCheckConfig;
  private api: AxiosInstance;
  private token: string | null = null;

  constructor(config: GreenCheckProviderConfig, location_id: number) {
    super();
    this.location_id = location_id;

    // Try to load environment variables manually if not available
    if (!process.env.GREEN_CHECK_CLIENT_ID) {
      try {
        require('dotenv').config({ path: '.env' });
        require('dotenv').config({ path: '/usr/src/app/.env' });
        require('dotenv').config({ path: '/usr/src/app/apps/platform/.env' });
      } catch (error) {
        // Ignore dotenv errors
      }
    }

    // Use environment variables with fallback to config, then hardcoded values as last resort
    const clientId = config.clientId || process.env.GREEN_CHECK_CLIENT_ID || '5fad96d5-4842-4377-ad48-57f7db6b8b2c';
    const clientSecret = config.clientSecret || process.env.GREEN_CHECK_CLIENT_SECRET || 'e5ec351d29eff5d0764d5f64d0619604e2e9042447c51d0880d1b58857ead3924bdd8de654456055f5488b0576ba955fb8f7d8f4398df6c9aa621324ccda1a83cb673856b6f0b7b39c24dde770f10cf31a121709772576ba29c517f5d16aabf5d873f1fd291f1959870429cd72bfecab4c63fccd106caaf2ee50dfd2cd20312d';
    const serviceProviderId = config.serviceProviderId || process.env.GREEN_CHECK_SERVICE_PROVIDER_ID || 'f2b48e57-7114-432d-8f11-a0d74b8fb934';
    const baseUrl = config.baseUrl || process.env.GREEN_CHECK_BASE_URL || 'https://sandbox-api.greencheckverified.com';

    // Debug logging
    logger.info({
      message: "Green Check Provider initialization",
      location_id: this.location_id,
      clientId: clientId ? clientId.substring(0, 8) + '...' : 'MISSING',
      serviceProviderId: serviceProviderId ? serviceProviderId.substring(0, 8) + '...' : 'MISSING',
      baseUrl,
      env_client_id: process.env.GREEN_CHECK_CLIENT_ID ? 'SET' : 'MISSING',
      env_client_secret: process.env.GREEN_CHECK_CLIENT_SECRET ? 'SET' : 'MISSING',
      env_service_provider_id: process.env.GREEN_CHECK_SERVICE_PROVIDER_ID ? 'SET' : 'MISSING',
      config_client_id: config.clientId ? 'SET' : 'MISSING',
      config_client_secret: config.clientSecret ? 'SET' : 'MISSING',
      config_service_provider_id: config.serviceProviderId ? 'SET' : 'MISSING',
      final_client_id: clientId ? 'SET' : 'MISSING',
      final_client_secret: clientSecret ? 'SET' : 'MISSING',
      final_service_provider_id: serviceProviderId ? 'SET' : 'MISSING'
    });

    if (!clientId || !clientSecret || !serviceProviderId) {
      throw new Error(`Green Check credentials are required. Missing: ${!clientId ? 'CLIENT_ID ' : ''}${!clientSecret ? 'CLIENT_SECRET ' : ''}${!serviceProviderId ? 'SERVICE_PROVIDER_ID' : ''}`);
    }

    this.config = {
      clientId,
      clientSecret,
      serviceProviderId,
      crbId: config.crbId, // This will be passed in from the specific location
      grantType: "client_credentials",
      scope: ['service-provider:read', 'point-of-sale:read']
    };

    this.api = axios.create({
      baseURL: baseUrl,
      headers: {
        "Content-Type": "application/json",
      },
    });

    this.api.interceptors.request.use(async (config) => {
      if (!this.token) {
        await this.authenticate();
      }
      config.headers.Authorization = `Bearer ${this.token}`;
      return config;
    });
  }

  private async authenticate(): Promise<void> {
    try {
      const baseUrl = process.env.GREEN_CHECK_BASE_URL || 'https://sandbox-api.greencheckverified.com';
      const authUrl = `${baseUrl}/auth/token`;

      // Debug logging before authentication
      logger.info({
        message: "Green Check authentication attempt",
        location_id: this.location_id,
        authUrl,
        hasClientId: !!this.config.clientId,
        hasClientSecret: !!this.config.clientSecret,
        hasServiceProviderId: !!this.config.serviceProviderId,
        clientIdLength: this.config.clientId ? this.config.clientId.length : 0,
        clientSecretLength: this.config.clientSecret ? this.config.clientSecret.length : 0,
        clientIdPreview: this.config.clientId ? this.config.clientId.substring(0, 8) + '...' : 'MISSING',
        serviceProviderIdPreview: this.config.serviceProviderId ? this.config.serviceProviderId.substring(0, 8) + '...' : 'MISSING'
      });

      const response = await axios.post(authUrl, {
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        grant_type: "client_credentials",
        scope: ["service-provider:read", "point-of-sale:read"]
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      this.token = response.data.access_token;
      logger.info({
        message: "Successfully authenticated with Green Check",
        location_id: this.location_id,
        auth_url: authUrl,
      });
    } catch (error: any) {
      const baseUrl = process.env.GREEN_CHECK_BASE_URL || 'https://sandbox-api.greencheckverified.com';
      logger.error({
        message: "Failed to authenticate with Green Check",
        error: error instanceof Error ? error.message : "Unknown error",
        location_id: this.location_id,
        auth_url: `${baseUrl}/auth/token`,
        client_id: this.config.clientId?.substring(0, 8) + '...',
        response_status: error.response?.status,
        response_data: error.response?.data,
      });
      throw new Error(`Green Check authentication failed: ${error.response?.data?.message || error.message}`);
    }
  }

  boot(): void {
    logger.info({
      message: "Green Check POS Provider initialized",
      location_id: this.location_id,
      service_provider_id: this.config.serviceProviderId,
      crb_id: this.config.crbId,
    });
  }

  async getCRBs(): Promise<GreenCheckCRB[]> {
    try {
      await this.authenticate();

      const response = await this.api.get(`/service-providers/${this.config.serviceProviderId}/crbs`);

      // The API returns an object with CRB IDs as keys, convert to array
      const crbsObject = response.data;
      const crbsArray = Object.values(crbsObject) as GreenCheckCRB[];

      logger.info(`Green Check: Retrieved ${crbsArray.length} CRBs`);
      return crbsArray;
    } catch (error) {
      logger.error('Green Check: Failed to get CRBs', error);
      throw error;
    }
  }

  async verify(): Promise<boolean> {
    try {
      // Verify by fetching CRB info
      const response = await this.api.get(
        `/service-providers/${this.config.serviceProviderId}/crbs/${this.config.crbId}`
      );
      return response.status === 200;
    } catch (error) {
      logger.error({
        message: "Green Check provider verification failed",
        error: error instanceof Error ? error.message : "Unknown error",
      });
      return false;
    }
  }

  async fetchTickets(startDate: Date, endDate: Date): Promise<PosTicket[]> {
    try {
      const response = await this.api.get(
        `/service-providers/${this.config.serviceProviderId}/crbs/${this.config.crbId}/sales`,
        {
          params: {
            start_date: startDate.toISOString().split('T')[0],
            end_date: endDate.toISOString().split('T')[0],
            limit: 100,
            offset: 0,
          },
        }
      );

      const sales: GreenCheckSale[] = response.data.data;
      return sales.map((sale) => this.convertSaleToTicket(sale));
    } catch (error) {
      logger.error({
        message: "Failed to fetch Green Check sales",
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  async fetchProducts(): Promise<PosProduct[]> {
    try {
      const response = await this.api.get(
        `/service-providers/${this.config.serviceProviderId}/crbs/${this.config.crbId}/products`
      );

      const products: GreenCheckProduct[] = response.data.data;
      return products.map((product) => this.convertGreenCheckProduct(product));
    } catch (error) {
      logger.error({
        message: "Failed to fetch Green Check products",
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  // Extended Green Check specific methods
  async syncSales(startDate: Date, endDate: Date): Promise<void> {
    try {
      // Fetch tickets from Green Check API
      const tickets = await this.fetchTickets(startDate, endDate);

      logger.info({
        message: "Fetched Green Check sales data",
        location_id: this.location_id,
        count: tickets.length,
        date_range: `${startDate.toISOString()} to ${endDate.toISOString()}`,
      });

      // Process each ticket and save to database
      let processed = 0;
      let errors = 0;

      for (const ticket of tickets) {
        try {
          // Convert ticket to PosData format
          const posData = this.normalizeTicketToPosData(ticket);

          // Check if this sale already exists (avoid duplicates)
          const existingSale = await App.main
            .db("pos_data")
            .where({
              location_id: this.location_id,
              order_date: posData.order_date,
              invoice_total: posData.invoice_total,
              customer_name: posData.customer_name,
            })
            .first();

          if (!existingSale) {
            // Insert new sale record
            await App.main.db("pos_data").insert({
              ...posData,
              created_at: new Date(),
              updated_at: new Date(),
            });
            processed++;
          } else {
            logger.debug({
              message: "Skipping duplicate sale",
              sale_id: ticket.id,
              location_id: this.location_id,
            });
          }
        } catch (error) {
          errors++;
          logger.error({
            message: "Failed to save individual sale",
            sale_id: ticket.id,
            error: error instanceof Error ? error.message : "Unknown error",
            location_id: this.location_id,
          });
        }
      }

      logger.info({
        message: "Completed Green Check sales sync",
        location_id: this.location_id,
        processed,
        errors,
        total: tickets.length,
      });
    } catch (error) {
      logger.error({
        message: "Failed to sync Green Check sales",
        error: error instanceof Error ? error.message : "Unknown error",
        location_id: this.location_id,
      });
      throw error;
    }
  }

  async syncCustomers(): Promise<void> {
    try {
      const response = await this.api.get(
        `/service-providers/${this.config.serviceProviderId}/crbs/${this.config.crbId}/customers`
      );

      const customers: GreenCheckCustomer[] = response.data.data;

      for (const customer of customers) {
        await this.syncCustomer(customer);
      }

      logger.info({
        message: "Successfully synced Green Check customers",
        location_id: this.location_id,
        count: customers.length,
      });
    } catch (error) {
      logger.error({
        message: "Failed to sync Green Check customers",
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  async syncInventory(): Promise<void> {
    try {
      const response = await this.api.get(
        `/service-providers/${this.config.serviceProviderId}/crbs/${this.config.crbId}/inventory`
      );

      // TODO: Process inventory data and save to database
      // For now, just log that we received the data
      logger.info({
        message: "Successfully synced Green Check inventory",
        location_id: this.location_id,
        inventory_count: response.data?.data?.length || 0,
      });
    } catch (error) {
      logger.error({
        message: "Failed to sync Green Check inventory",
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  async syncLocation(): Promise<void> {
    try {
      const response = await this.api.get(
        `/service-providers/${this.config.serviceProviderId}/crbs/${this.config.crbId}`
      );

      const location: GreenCheckLocation = response.data;
      await this.updateLocationFromGreenCheck(location);

      logger.info({
        message: "Successfully synced Green Check location",
        location_id: this.location_id,
      });
    } catch (error) {
      logger.error({
        message: "Failed to sync Green Check location",
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  private convertSaleToTicket(sale: GreenCheckSale): PosTicket {
    const items: PosTicketItem[] = sale.line_items.map((item) => ({
      id: item.pos_product_id || item.product_id || `item_${Math.random()}`,
      name: item.product_name || `Product ${item.pos_product_id || 'Unknown'}`,
      quantity: item.num_units,
      price: item.price_per_unit,
      category: item.product_type || 'Unknown',
      subcategory: item.product_type || 'Unknown',
      cost: 0, // Not directly available in Green Check data
    }));

    // Calculate loyalty discounts from line items if available
    const loyaltyDiscount = this.calculateLoyaltyDiscounts(sale.line_items);

    return {
      id: sale.id,
      date: new Date(sale.date),
      total: sale.total_paid,
      customer_name: sale.customer?.customer_id || 'Anonymous',
      customer_id: sale.customer?.customer_id,
      customer_type: sale.customer?.type || 'rec',
      customer_birth_date: sale.customer?.dob
        ? new Date(sale.customer.dob)
        : undefined,
      budtender_name: sale.employee_id || 'Unknown',
      location_name: "", // Will be filled from location sync
      returned_amount: sale.transaction_type === "return" ? sale.total_paid : 0,
      discounted_amount: sale.total_discounts,
      loyalty_as_discount: loyaltyDiscount,
      loyalty_as_payment: 0, // Calculate from payments if needed
      tax_amount: sale.tax_paid,
      amount_paid_in_cash: this.calculateCashPayments(sale.payments || []),
      amount_paid_in_debit: this.calculateCardPayments(sale.payments || []),
      inventory_cost: 0, // Calculate from items if needed
      inventory_profit: 0, // Calculate from items if needed
      items,
    };
  }

  private convertGreenCheckProduct(product: GreenCheckProduct): PosProduct {
    return {
      id: product.id,
      name: product.name,
      description: product.description,
      category: product.pos_product_category_name,
      subcategory: product.pos_product_subcategory_name,
      price: product.price || 0,
      inventory_quantity: 0, // Will be updated from inventory sync
      sku: product.sku,
      barcode: "", // Not available in Green Check data
      brand: product.pos_brand_name,
      vendor: "", // Not available in Green Check data
      is_active: true, // Assume active if in Green Check
    };
  }

  private async syncCustomer(customer: GreenCheckCustomer): Promise<void> {
    try {
      // Check if customer already exists
      const user = await App.main
        .db("users")
        .where({
          location_id: this.location_id,
          pos_customer_id: customer.pos_customer_id,
        })
        .first();

      const userData = {
        location_id: this.location_id,
        pos_customer_id: customer.pos_customer_id,
        crb_id: customer.crb_id,
        pos_type: customer.pos_type,
        first_name: customer.first_name,
        last_name: customer.last_name,
        middle_name: customer.middle_name,
        email: customer.email_address,
        phone: customer.phone,
        cell_phone: customer.cell_phone,
        birth_date: customer.dob ? new Date(customer.dob) : null,
        address_1: customer.address_1,
        address_2: customer.address_2,
        city: customer.city,
        state: customer.state,
        zip_code: customer.zip_code,
        gender: customer.gender,
        pos_identification_type: customer.pos_identification_type,
        pos_customer_type: customer.pos_customer_type,
        gc_customer_type: customer.gc_customer_type,
        medical_id: customer.medical_id,
        medical_id_expiration_date: customer.medical_id_expiration_date
          ? new Date(customer.medical_id_expiration_date)
          : null,
        drivers_license_id: customer.drivers_license_id,
        drivers_license_expiration_date:
          customer.drivers_license_expiration_date
            ? new Date(customer.drivers_license_expiration_date)
            : null,
        other_id: customer.other_id,
        other_id_expiration_date: customer.other_id_expiration_date,
        caregiver: customer.caregiver,
        loyalty_member: customer.loyalty_member,
        loyalty_points: customer.loyalty_points,
        anonymous: customer.anonymous,
        pos_updated_date: new Date(customer.pos_updated_date),
        pos_updated_date_local: customer.pos_updated_date_local
          ? new Date(customer.pos_updated_date_local)
          : null,
        gc_created_date: new Date(customer.gc_created_date),
        gc_created_date_local: new Date(customer.gc_created_date_local),
        updated_at: new Date(),
      };

      if (user) {
        // Update existing user
        await App.main.db("users").where({ id: user.id }).update(userData);
      } else {
        // Create new user
        await App.main.db("users").insert({
          ...userData,
          created_at: new Date(),
        });
      }
    } catch (error) {
      logger.error({
        message: "Failed to sync customer",
        customer_id: customer.id,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  private async updateLocationFromGreenCheck(
    location: GreenCheckLocation
  ): Promise<void> {
    try {
      await App.main
        .db("locations")
        .where({ id: this.location_id })
        .update({
          pos_location_id: location.id,
          name: location.name,
          street_address: location.street_address,
          street_address_2: location.street_address_2,
          city: location.city,
          state: location.state,
          postal_code: location.postal_code,
          country: location.country,
          phone: location.phone_number,
          website: location.website,
          timezone: location.timezone,
          ein: location.ein,
          dba: location.dba,
          entity_type: location.entityType,
          business_type: location.business_type,
          org_type: location.org_type,
          established_date: new Date(location.established_date),
          ft_employees: location.ftEmployees,
          pt_employees: location.ptEmployees,
          monthly_sales: location.monthlySales,
          monthly_customers: location.monthlyCustomers,
          status: location.status,
          template_id: location.template_id,
          template_result_id: location.template_result_id,
          pos_configs: location.pos_configs,
          mailing_street_address: location.mailing_street_address,
          mailing_city: location.mailing_city,
          mailing_state: location.mailing_state,
          mailing_postal_code: location.mailing_postal_code,
          updated_at: new Date(),
        });
    } catch (error) {
      logger.error({
        message: "Failed to update location from Green Check",
        location_id: this.location_id,
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  private calculateLoyaltyDiscounts(lineItems: GreenCheckLineItem[]): number {
    return lineItems.reduce((total, item) => {
      const itemDiscounts = item.line_item_discounts || [];
      const loyaltyDiscounts = itemDiscounts.filter(discount =>
        discount.name?.toLowerCase().includes('loyalty') ||
        discount.name?.toLowerCase().includes('points')
      );
      return total + loyaltyDiscounts.reduce((sum, discount) => sum + (discount.flat_amount || 0), 0);
    }, 0);
  }

  private calculateCashPayments(payments: any[]): number {
    return payments
      .filter((p) => p.type === "cash")
      .reduce((sum, p) => sum + (p.amount_paid || 0), 0);
  }

  private calculateCardPayments(payments: any[]): number {
    return payments
      .filter((p) => p.type === "card" || p.type === "debit")
      .reduce((sum, p) => sum + (p.amount_paid || 0), 0);
  }

  // Override the base method to use Green Check specific data transformation
  normalizeTicketToPosData(ticket: PosTicket): Partial<PosData> {
    const baseData = super.normalizeTicketToPosData(ticket);

    // Add Green Check specific fields
    return {
      ...baseData,
      customer_id: ticket.customer_id,
      customer_email: undefined, // Will be resolved from customer sync
      customer_phone: undefined, // Will be resolved from customer sync
    };
  }
}
