exports.up = async function (knex) {
  await knex.schema.alterTable("locations", function (table) {
    // Add pos_provider field to track which POS system is connected
    table.string("pos_provider").nullable(); // e.g., "dutchie", "greencheck", "marijuanasoftware", etc.

    // Add index for performance
    table.index("pos_provider");
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("locations", function (table) {
    // Drop index first
    table.dropIndex("pos_provider");

    // Drop column
    table.dropColumn("pos_provider");
  });
};
