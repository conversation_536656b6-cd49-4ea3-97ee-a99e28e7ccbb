exports.up = async function (knex) {
  await knex.schema.alterTable("pos_data", function (table) {
    // Add order_id field to track which order the POS sale belongs to
    table.integer("order_id").unsigned().nullable();

    // Add foreign key constraint to orders table
    table
      .foreign("order_id")
      .references("id")
      .inTable("orders")
      .onDelete("SET NULL"); // Set to NULL if order is deleted

    // Add index for performance
    table.index("order_id");
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("pos_data", function (table) {
    // Drop foreign key constraint first
    table.dropForeign("order_id");
    // Drop the column
    table.dropColumn("order_id");
  });
};
