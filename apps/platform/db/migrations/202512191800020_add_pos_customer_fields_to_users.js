exports.up = async function (knex) {
  await knex.schema.alterTable("users", function (table) {
    // POS System Integration Fields
    table.string("pos_type").nullable(); // e.g., "BioTrack", "Greenbits", "Treez"
    table.string("pos_customer_id").nullable(); // ID from the POS system
    table.string("crb_id").nullable(); // Cannabis Regulatory Board ID

    // Extended Name Fields
    table.string("first_name").nullable(); // Move from JSON data to direct column
    table.string("last_name").nullable(); // Move from JSON data to direct column
    table.string("middle_name").nullable();

    // Address Information
    table.string("address_1").nullable();
    table.string("address_2").nullable();
    table.string("city").nullable();
    table.string("state").nullable();
    table.string("zip_code").nullable();

    // Contact Information
    table.string("cell_phone").nullable(); // Separate from main phone

    // Personal Information
    table.string("gender").nullable();

    // Cannabis-Specific Fields
    table.string("pos_identification_type").nullable(); // Type of ID used at POS
    table.string("pos_customer_type").nullable(); // e.g., "medical", "recreational"
    table.string("gc_customer_type").nullable(); // Green Cannabis customer type

    // Medical Cannabis Fields
    table.string("medical_id").nullable();
    table.date("medical_id_expiration_date").nullable();

    // Driver's License Fields
    table.string("drivers_license_id").nullable();
    table.date("drivers_license_expiration_date").nullable();

    // Other Identification Fields
    table.string("other_id").nullable();
    table.string("other_id_expiration_date").nullable(); // Note: POS data shows this as string, not date

    // Boolean Flags
    table.boolean("caregiver").nullable().defaultTo(false);
    table.boolean("loyalty_member").nullable().defaultTo(false);
    table.boolean("anonymous").nullable().defaultTo(false);

    // Loyalty Points
    table.decimal("loyalty_points", 15, 6).nullable().defaultTo(0); // High precision for points

    // POS Timestamps
    table.timestamp("pos_updated_date").nullable();
    table.timestamp("pos_updated_date_local").nullable();
    table.timestamp("gc_created_date").nullable();
    table.timestamp("gc_created_date_local").nullable();

    // Add indexes for commonly queried fields
    table.index("pos_customer_id");
    table.index("crb_id");
    table.index("medical_id");
    table.index("drivers_license_id");
    table.index("pos_type");
    table.index("pos_customer_type");
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("users", function (table) {
    // Drop in reverse order
    table.dropIndex("pos_customer_type");
    table.dropIndex("pos_type");
    table.dropIndex("drivers_license_id");
    table.dropIndex("medical_id");
    table.dropIndex("crb_id");
    table.dropIndex("pos_customer_id");

    // Drop columns
    table.dropColumn("gc_created_date_local");
    table.dropColumn("gc_created_date");
    table.dropColumn("pos_updated_date_local");
    table.dropColumn("pos_updated_date");
    table.dropColumn("loyalty_points");
    table.dropColumn("anonymous");
    table.dropColumn("loyalty_member");
    table.dropColumn("caregiver");
    table.dropColumn("other_id_expiration_date");
    table.dropColumn("other_id");
    table.dropColumn("drivers_license_expiration_date");
    table.dropColumn("drivers_license_id");
    table.dropColumn("medical_id_expiration_date");
    table.dropColumn("medical_id");
    table.dropColumn("gc_customer_type");
    table.dropColumn("pos_customer_type");
    table.dropColumn("pos_identification_type");
    table.dropColumn("gender");
    table.dropColumn("cell_phone");
    table.dropColumn("zip_code");
    table.dropColumn("state");
    table.dropColumn("city");
    table.dropColumn("address_2");
    table.dropColumn("address_1");
    table.dropColumn("middle_name");
    table.dropColumn("last_name");
    table.dropColumn("first_name");
    table.dropColumn("crb_id");
    table.dropColumn("pos_customer_id");
    table.dropColumn("pos_type");
  });
};
