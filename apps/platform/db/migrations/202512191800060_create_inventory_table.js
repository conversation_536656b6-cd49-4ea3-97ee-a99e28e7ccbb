exports.up = async function (knex) {
  return knex.schema.createTable("inventory", (table) => {
    table.increments("id").primary();

    // Location and Product References
    table.integer("location_id").unsigned().notNullable().index();
    table.string("product_id").nullable(); // Our internal product ID
    table.string("pos_product_id").nullable(); // POS system product ID
    table.string("crb_id").nullable(); // Cannabis Regulatory Board ID

    // Inventory Quantities
    table.decimal("quantity_on_hand", 12, 3).nullable().defaultTo(0);
    table.decimal("quantity_available", 12, 3).nullable().defaultTo(0);
    table.decimal("quantity_reserved", 12, 3).nullable().defaultTo(0);
    table.decimal("quantity_committed", 12, 3).nullable().defaultTo(0);

    // Weight-based tracking (for cannabis products)
    table.decimal("grams_on_hand", 12, 3).nullable().defaultTo(0);
    table.decimal("grams_available", 12, 3).nullable().defaultTo(0);

    // Pricing Information
    table.decimal("unit_cost", 12, 2).nullable();
    table.decimal("unit_price", 12, 2).nullable();
    table.decimal("wholesale_price", 12, 2).nullable();
    table.decimal("retail_price", 12, 2).nullable();

    // POS Integration Fields
    table.string("pos_type").nullable(); // e.g., "BioTrack", "Greenbits", "Treez"
    table.string("inventory_location_id").nullable(); // Sub-location within store
    table.string("inventory_location_name").nullable(); // e.g., "Main Floor", "Storage"

    // Product Information (cached from products table)
    table.string("product_name").nullable();
    table.string("product_sku").nullable();
    table.string("product_category").nullable();
    table.string("product_subcategory").nullable();
    table.string("brand_name").nullable();
    table.string("strain_name").nullable();

    // Cannabis-Specific Fields
    table.boolean("is_cannabis_product").nullable().defaultTo(false);
    table.decimal("thc_percentage", 8, 5).nullable(); // High precision for percentages
    table.decimal("cbd_percentage", 8, 5).nullable();
    table.string("batch_number").nullable();
    table.date("harvest_date").nullable();
    table.date("expiration_date").nullable();

    // Status and Tracking
    table.string("status").nullable().defaultTo("active"); // active, inactive, discontinued
    table.boolean("is_trackable").nullable().defaultTo(true); // Whether item requires tracking
    table.timestamp("last_counted_at").nullable(); // Last physical inventory count
    table.timestamp("last_movement_at").nullable(); // Last inventory movement

    // POS Timestamps
    table.timestamp("pos_updated_date").nullable();
    table.timestamp("pos_updated_date_local").nullable();

    // Standard timestamps
    table.timestamp("created_at").notNullable().defaultTo(knex.fn.now());
    table.timestamp("updated_at").notNullable().defaultTo(knex.fn.now());

    // Foreign key constraints
    table
      .foreign("location_id")
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");

    // Indexes for performance
    table.index("product_id");
    table.index("pos_product_id");
    table.index("crb_id");
    table.index("pos_type");
    table.index("product_sku");
    table.index("batch_number");
    table.index("status");
    table.index("is_cannabis_product");
    table.index(["location_id", "product_id"]); // Composite index for location-product queries
    table.index(["location_id", "pos_product_id"]); // Composite index for POS integration
  });
};

exports.down = async function (knex) {
  return knex.schema.dropTable("inventory");
};
