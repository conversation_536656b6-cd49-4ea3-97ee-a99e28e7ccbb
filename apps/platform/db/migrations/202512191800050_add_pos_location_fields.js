exports.up = async function (knex) {
  await knex.schema.alterTable("locations", function (table) {
    // POS Integration Fields
    table.string("pos_location_id").nullable(); // External ID from POS partner

    // Business Information (cannabis-specific)
    table.string("ein").nullable(); // Employer Identification Number
    table.string("dba").nullable(); // Doing Business As name
    table.string("entity_type").nullable(); // e.g., "sole", "llc", "corporation"
    table.string("business_type").nullable(); // e.g., "retail", "cultivation", "manufacturing"
    table.string("org_type").nullable(); // Organization type from POS partner
    table.date("established_date").nullable(); // When business was established

    // Employee Information
    table.integer("ft_employees").nullable().defaultTo(0); // Full-time employees
    table.integer("pt_employees").nullable().defaultTo(0); // Part-time employees

    // Business Metrics
    table.string("monthly_sales").nullable(); // Monthly sales volume (stored as string for flexibility)
    table.integer("monthly_customers").nullable().defaultTo(0); // Monthly customer count

    // Mailing Address (separate from physical address)
    table.string("mailing_street_address").nullable();
    table.string("mailing_street_address_2").nullable();
    table.string("mailing_city").nullable();
    table.string("mailing_state").nullable();
    table.string("mailing_postal_code").nullable();

    // Address field adjustments (complement existing address)
    table.string("street_address").nullable(); // More specific than generic "address"
    table.string("street_address_2").nullable(); // Address line 2
    table.string("postal_code").nullable(); // More specific than "zip"

    // Status Tracking
    table.string("status").nullable(); // e.g., "active", "inactive", "bank_disconnected"

    // POS Configuration (stored as JSON for flexibility)
    table.json("pos_configs").nullable(); // Array of POS system configurations

    // Template/Partner Integration
    table.string("template_id").nullable(); // Partner template ID
    table.string("template_result_id").nullable(); // Template result ID

    // Add indexes for commonly queried fields
    table.index("pos_location_id");
    table.index("ein");
    table.index("business_type");
    table.index("status");
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("locations", function (table) {
    // Drop indexes first
    table.dropIndex("status");
    table.dropIndex("business_type");
    table.dropIndex("ein");
    table.dropIndex("pos_location_id");

    // Drop columns
    table.dropColumn("template_result_id");
    table.dropColumn("template_id");
    table.dropColumn("pos_configs");
    table.dropColumn("status");
    table.dropColumn("postal_code");
    table.dropColumn("street_address_2");
    table.dropColumn("street_address");
    table.dropColumn("mailing_postal_code");
    table.dropColumn("mailing_state");
    table.dropColumn("mailing_city");
    table.dropColumn("mailing_street_address_2");
    table.dropColumn("mailing_street_address");
    table.dropColumn("monthly_customers");
    table.dropColumn("monthly_sales");
    table.dropColumn("pt_employees");
    table.dropColumn("ft_employees");
    table.dropColumn("established_date");
    table.dropColumn("org_type");
    table.dropColumn("business_type");
    table.dropColumn("entity_type");
    table.dropColumn("dba");
    table.dropColumn("ein");
    table.dropColumn("pos_location_id");
  });
};
