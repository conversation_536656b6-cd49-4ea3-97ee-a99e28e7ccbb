exports.up = async function (knex) {
  await knex.schema.alterTable("products", function (table) {
    // POS System Integration Fields
    table.string("pos_type").nullable(); // e.g., "BioTrack", "Greenbits", "Treez", "Manual Upload Retail"
    table.string("pos_product_id").nullable(); // ID from the POS system
    table.string("crb_id").nullable(); // Cannabis Regulatory Board ID for products

    // Green Cannabis specific fields (these are new and needed)
    table.string("gc_product_category_name").nullable(); // Green Cannabis category
    table.decimal("gc_net_weight_grams", 10, 3).nullable(); // Net weight in grams
    table.timestamp("gc_created_date").nullable();
    table.timestamp("gc_created_date_local").nullable();

    // POS Unit of Measure (new field needed)
    table.string("pos_unit_of_measure").nullable(); // Unit of measure (e.g., "Wgt", "Qty")

    // Price Tiers (new field for complex pricing)
    table.json("price_tiers").nullable(); // Array of price tier objects

    // POS Updated Date (separate from our updated_at)
    table.timestamp("pos_updated_date").nullable();

    // Add indexes for commonly queried POS fields
    table.index("pos_product_id");
    table.index("crb_id");
    table.index("pos_type");
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("products", function (table) {
    // Drop indexes first
    table.dropIndex("pos_type");
    table.dropIndex("crb_id");
    table.dropIndex("pos_product_id");

    // Drop columns
    table.dropColumn("pos_updated_date");
    table.dropColumn("price_tiers");
    table.dropColumn("pos_unit_of_measure");
    table.dropColumn("gc_created_date_local");
    table.dropColumn("gc_created_date");
    table.dropColumn("gc_net_weight_grams");
    table.dropColumn("gc_product_category_name");
    table.dropColumn("crb_id");
    table.dropColumn("pos_product_id");
    table.dropColumn("pos_type");
  });
};
