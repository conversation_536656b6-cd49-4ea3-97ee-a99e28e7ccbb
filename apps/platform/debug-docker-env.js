/**
 * Debug script to check environment variables inside Docker
 */

require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '../../.env' });

console.log('🐳 Docker Environment Variable Debug\n');

console.log('📋 Green Check Environment Variables:');
console.log(`   GREEN_CHECK_CLIENT_ID: ${process.env.GREEN_CHECK_CLIENT_ID ? '✅ Set' : '❌ Missing'}`);
console.log(`   GREEN_CHECK_CLIENT_SECRET: ${process.env.GREEN_CHECK_CLIENT_SECRET ? '✅ Set (length: ' + process.env.GREEN_CHECK_CLIENT_SECRET.length + ')' : '❌ Missing'}`);
console.log(`   GREEN_CHECK_SERVICE_PROVIDER_ID: ${process.env.GREEN_CHECK_SERVICE_PROVIDER_ID ? '✅ Set' : '❌ Missing'}`);
console.log(`   GREEN_CHECK_BASE_URL: ${process.env.GREEN_CHECK_BASE_URL || 'https://sandbox-api.greencheckverified.com (default)'}`);
console.log(`   GREEN_CHECK_ENVIRONMENT: ${process.env.GREEN_CHECK_ENVIRONMENT || 'sandbox (default)'}`);

console.log('\n📋 Other Key Environment Variables:');
console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
console.log(`   PORT: ${process.env.PORT || 'not set'}`);
console.log(`   DB_HOST: ${process.env.DB_HOST || 'not set'}`);

// Test loading the app environment
console.log('\n🔧 Testing App Environment Loading...');
try {
  const env = require('./src/config/env.ts').default();
  console.log('✅ App environment loaded successfully');
  console.log(`   Green Check Client ID: ${env.greenCheck?.clientId ? '✅ Set' : '❌ Missing'}`);
  console.log(`   Green Check Base URL: ${env.greenCheck?.baseUrl || 'not set'}`);
} catch (error) {
  console.log('❌ Failed to load app environment:', error.message);
}

console.log('\n🔍 All Environment Variables:');
const greenCheckVars = Object.keys(process.env).filter(key => key.startsWith('GREEN_CHECK_'));
if (greenCheckVars.length > 0) {
  greenCheckVars.forEach(key => {
    const value = process.env[key];
    console.log(`   ${key}: ${value ? (key.includes('SECRET') ? '[REDACTED]' : value) : 'not set'}`);
  });
} else {
  console.log('   No GREEN_CHECK_ variables found');
}
