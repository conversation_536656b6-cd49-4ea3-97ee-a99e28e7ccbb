/**
 * Direct test of Green Check API with hardcoded credentials
 */

const axios = require('axios');

console.log('🔍 Direct Green Check API Test with Hardcoded Credentials...\n');

// Hardcoded credentials
const CLIENT_ID = '5fad96d5-4842-4377-ad48-57f7db6b8b2c';
const CLIENT_SECRET = 'e5ec351d29eff5d0764d5f64d0619604e2e9042447c51d0880d1b58857ead3924bdd8de654456055f5488b0576ba955fb8f7d8f4398df6c9aa621324ccda1a83cb673856b6f0b7b39c24dde770f10cf31a121709772576ba29c517f5d16aabf5d873f1fd291f1959870429cd72bfecab4c63fccd106caaf2ee50dfd2cd20312d';
const SERVICE_PROVIDER_ID = 'f2b48e57-7114-432d-8f11-a0d74b8fb934';
const BASE_URL = 'https://sandbox-api.greencheckverified.com';

async function testGreenCheckAPI() {
  try {
    console.log('🔐 Step 1: Testing Authentication...');
    
    // Test authentication
    const authResponse = await axios.post(`${BASE_URL}/auth/token`, {
      client_id: CLIENT_ID,
      client_secret: CLIENT_SECRET,
      grant_type: 'client_credentials',
      scope: ['service-provider:read', 'point-of-sale:read']
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Authentication successful!');
    console.log(`   Token type: ${authResponse.data.token_type}`);
    console.log(`   Expires in: ${authResponse.data.expires_in} seconds`);
    
    const accessToken = authResponse.data.access_token;
    
    console.log('\n🏢 Step 2: Testing Get CRBs...');
    
    // Test getting CRBs
    const crbResponse = await axios.get(`${BASE_URL}/service-providers/${SERVICE_PROVIDER_ID}/crbs`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Get CRBs successful!');
    console.log(`   Found ${crbResponse.data.length} CRBs`);
    
    if (crbResponse.data.length > 0) {
      const firstCrb = crbResponse.data[0];
      console.log(`   First CRB: ${firstCrb.name} (ID: ${firstCrb.id})`);
      console.log(`   Status: ${firstCrb.status}`);
      console.log(`   Type: ${firstCrb.type}`);
      
      console.log('\n📊 Step 3: Testing Get Sales Data...');
      
      // Test getting sales data for the first CRB
      try {
        const salesResponse = await axios.get(`${BASE_URL}/crbs/${firstCrb.id}/sales`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          params: {
            start_date: '2024-01-01',
            end_date: '2024-12-31'
          }
        });
        
        console.log('✅ Get Sales Data successful!');
        console.log(`   Found ${salesResponse.data.length} sales records`);
        
        if (salesResponse.data.length > 0) {
          console.log(`   First sale: ${JSON.stringify(salesResponse.data[0], null, 2)}`);
        }
        
      } catch (salesError) {
        console.log('⚠️  Sales data request failed (this might be expected):');
        console.log(`   Status: ${salesError.response?.status}`);
        console.log(`   Message: ${salesError.response?.data?.message || salesError.message}`);
      }
    }
    
    console.log('\n🎉 Green Check API integration test completed successfully!');
    
  } catch (error) {
    console.log('❌ Green Check API test failed:');
    console.log(`   Status: ${error.response?.status}`);
    console.log(`   Error: ${error.response?.data?.message || error.message}`);
    if (error.response?.data) {
      console.log(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

testGreenCheckAPI();
