/**
 * Test GreenCheckProvider instantiation in the actual application context
 */

// Load environment variables first
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '/usr/src/app/.env' });
require('dotenv').config({ path: '/usr/src/app/apps/platform/.env' });

console.log('🔍 Testing GreenCheckProvider Instantiation in App Context...\n');

async function testProviderInstantiation() {
  try {
    // Check environment variables
    console.log('📋 Environment Variables:');
    const greenCheckVars = [
      'GREEN_CHECK_CLIENT_ID',
      'GREEN_CHECK_CLIENT_SECRET', 
      'GREEN_CHECK_SERVICE_PROVIDER_ID',
      'GREEN_CHECK_BASE_URL',
      'GREEN_CHECK_ENVIRONMENT'
    ];

    greenCheckVars.forEach(varName => {
      const value = process.env[varName];
      console.log(`   ${varName}: ${value ? '✅ SET' : '❌ MISSING'}`);
    });

    // Try to create a GreenCheckProvider instance like the application would
    console.log('\n🏗️  Creating GreenCheckProvider instance...');
    
    // Simulate the configuration that would come from the application
    const testConfig = {
      crbId: '4185f661-940a-499f-a05f-d8fe777b59b6', // From our test results
    };
    
    // Import the GreenCheckProvider class
    const GreenCheckProvider = require('./build/providers/pos/GreenCheckProvider').default;
    
    console.log('✅ GreenCheckProvider class imported');
    
    // Create instance
    const provider = new GreenCheckProvider(testConfig, 1);
    console.log('✅ GreenCheckProvider instance created');
    
    // Boot the provider
    provider.boot();
    console.log('✅ Provider booted');
    
    // Try to verify (this will trigger authentication)
    console.log('\n🔐 Testing verification (triggers authentication)...');
    const isVerified = await provider.verify();
    
    if (isVerified) {
      console.log('✅ Provider verification successful!');
    } else {
      console.log('❌ Provider verification failed');
    }
    
    console.log('\n🎉 Test completed successfully!');
    
  } catch (error) {
    console.log('\n❌ Test failed:');
    console.log(`   Error: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }
}

testProviderInstantiation();
