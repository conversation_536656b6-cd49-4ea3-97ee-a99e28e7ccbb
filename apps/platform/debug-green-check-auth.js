/**
 * Debug script for Green Check authentication issues
 */

require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '../../.env' });

const axios = require('axios');

async function debugGreenCheckAuth() {
  console.log('🔍 Debugging Green Check Authentication...\n');

  // Step 1: Check environment variables
  console.log('📋 Step 1: Checking environment variables...');
  const clientId = process.env.GREEN_CHECK_CLIENT_ID;
  const clientSecret = process.env.GREEN_CHECK_CLIENT_SECRET;
  const serviceProviderId = process.env.GREEN_CHECK_SERVICE_PROVIDER_ID;
  const baseUrl = process.env.GREEN_CHECK_BASE_URL;
  const environment = process.env.GREEN_CHECK_ENVIRONMENT;

  console.log(`   CLIENT_ID: ${clientId ? '✅ Set' : '❌ Missing'}`);
  console.log(`   CLIENT_SECRET: ${clientSecret ? '✅ Set (length: ' + clientSecret.length + ')' : '❌ Missing'}`);
  console.log(`   SERVICE_PROVIDER_ID: ${serviceProviderId ? '✅ Set' : '❌ Missing'}`);
  console.log(`   BASE_URL: ${baseUrl || 'https://sandbox-api.greencheckverified.com (default)'}`);
  console.log(`   ENVIRONMENT: ${environment || 'sandbox (default)'}`);

  if (!clientId || !clientSecret || !serviceProviderId) {
    console.log('\n❌ Missing required environment variables!');
    console.log('Please ensure these are set in your .env file:');
    console.log('   GREEN_CHECK_CLIENT_ID=your_client_id');
    console.log('   GREEN_CHECK_CLIENT_SECRET=your_client_secret');
    console.log('   GREEN_CHECK_SERVICE_PROVIDER_ID=your_service_provider_id');
    return;
  }

  // Step 2: Test direct API call
  console.log('\n🌐 Step 2: Testing direct API authentication...');
  
  const authUrl = (baseUrl || 'https://sandbox-api.greencheckverified.com') + '/auth/token';
  console.log(`   Auth URL: ${authUrl}`);

  try {
    // Test the exact same call as in the provider
    const response = await axios.post(authUrl, {
      client_id: clientId,
      client_secret: clientSecret,
      grant_type: "client_credentials",
      scope: ["service-provider:read", "point-of-sale:read"]
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ Authentication successful!');
    console.log(`   Status: ${response.status}`);
    console.log(`   Token type: ${response.data.token_type}`);
    console.log(`   Expires at: ${new Date(response.data.expires_at * 1000).toISOString()}`);
    console.log(`   Scope: ${response.data.scope?.join(', ')}`);

    // Step 3: Test CRB access
    console.log('\n🏢 Step 3: Testing CRB access...');
    const token = response.data.access_token;
    
    try {
      const crbResponse = await axios.get(
        `${baseUrl || 'https://sandbox-api.greencheckverified.com'}/service-providers/${serviceProviderId}/crbs`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      console.log('✅ CRB access successful!');
      console.log(`   Found ${Object.keys(crbResponse.data).length} CRBs`);
      
      // List available CRBs
      console.log('\n📍 Available CRBs:');
      Object.values(crbResponse.data).forEach((crb, index) => {
        console.log(`   ${index + 1}. ${crb.name} (ID: ${crb.id})`);
        console.log(`      POS: ${crb.pos_configs?.[0]?.name || 'Unknown'}`);
        console.log(`      Status: ${crb.status}`);
      });

    } catch (crbError) {
      console.log('❌ CRB access failed!');
      console.log(`   Status: ${crbError.response?.status}`);
      console.log(`   Error: ${crbError.response?.data?.message || crbError.message}`);
    }

  } catch (authError) {
    console.log('❌ Authentication failed!');
    console.log(`   Status: ${authError.response?.status}`);
    console.log(`   Status Text: ${authError.response?.statusText}`);
    
    if (authError.response?.data) {
      console.log(`   Response Data:`, JSON.stringify(authError.response.data, null, 2));
    }
    
    console.log(`   Error Message: ${authError.message}`);

    // Common error scenarios
    console.log('\n🔧 Troubleshooting suggestions:');
    
    if (authError.response?.status === 401) {
      console.log('   • Check if your CLIENT_ID and CLIENT_SECRET are correct');
      console.log('   • Verify credentials are for the correct environment (sandbox vs production)');
    } else if (authError.response?.status === 403) {
      console.log('   • Your credentials may not have the required permissions');
      console.log('   • Contact Green Check support to verify your account access');
    } else if (authError.response?.status === 400) {
      console.log('   • Check the request format - may be missing required fields');
      console.log('   • Verify grant_type and scope parameters');
    } else if (authError.code === 'ECONNREFUSED' || authError.code === 'ENOTFOUND') {
      console.log('   • Network connectivity issue');
      console.log('   • Check if the API URL is correct');
      console.log('   • Verify firewall/proxy settings');
    } else if (authError.code === 'ETIMEDOUT') {
      console.log('   • Request timed out - API may be slow or unavailable');
      console.log('   • Try again in a few minutes');
    }
  }

  // Step 4: Test with curl command format
  console.log('\n🔧 Step 4: Equivalent curl command for manual testing:');
  console.log(`curl --location '${authUrl}' \\`);
  console.log(`--header 'Content-Type: application/json' \\`);
  console.log(`--data '{`);
  console.log(`  "client_id": "${clientId}",`);
  console.log(`  "client_secret": "${clientSecret}",`);
  console.log(`  "grant_type": "client_credentials",`);
  console.log(`  "scope": ["service-provider:read", "point-of-sale:read"]`);
  console.log(`}'`);
}

// Run the debug script
debugGreenCheckAuth().catch(console.error);
