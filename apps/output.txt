(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker cp test-env-loading-direct.js $(docker-compose ps -q api):/usr/src/app/apps/platform/
Successfully copied 5.12kB to ba87f111d1c902dfae30ba723348e6eb20b3b2f5eb982f485ab2793ba621b322:/usr/src/app/apps/platform/
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker-compose exec api node test-env-loading-direct.js 
🔍 Direct Environment Variable Loading Test...

📂 Current working directory: /usr/src/app/apps/platform

📁 Checking .env file existence:
   .env: ✅ EXISTS
      Contains GREEN_CHECK vars: ✅ YES
      GREEN_CHECK lines found: 5
   /usr/src/app/.env: ❌ MISSING
   /usr/src/app/apps/platform/.env: ✅ EXISTS
      Contains GREEN_CHECK vars: ✅ YES
      GREEN_CHECK lines found: 5
   ../../.env: ❌ MISSING

🔧 Current Environment Variables:
   GREEN_CHECK_CLIENT_ID: ❌ MISSING
   GREEN_CHECK_CLIENT_SECRET: ❌ MISSING
   GREEN_CHECK_SERVICE_PROVIDER_ID: ❌ MISSING
   GREEN_CHECK_BASE_URL: ❌ MISSING
   GREEN_CHECK_ENVIRONMENT: ❌ MISSING

🔄 Manual dotenv loading:
   .env: ✅ LOADED
   /usr/src/app/apps/platform/.env: ✅ LOADED

🔍 Environment Variables after manual loading:
   GREEN_CHECK_CLIENT_ID: ✅ SET
      Value: 5fad96d5-4842-4377-ad48-57f7db6b8b2c
   GREEN_CHECK_CLIENT_SECRET: ✅ SET
   GREEN_CHECK_SERVICE_PROVIDER_ID: ✅ SET
      Value: f2b48e57-7114-432d-8f11-a0d74b8fb934
   GREEN_CHECK_BASE_URL: ✅ SET
      Value: https://sandbox-api.greencheckverified.com
   GREEN_CHECK_ENVIRONMENT: ✅ SET
      Value: sandbox

🧪 Testing direct credential setting:
✅ Credentials set directly in process.env
   GREEN_CHECK_CLIENT_ID: ✅ SET
   GREEN_CHECK_CLIENT_SECRET: ✅ SET
   GREEN_CHECK_SERVICE_PROVIDER_ID: ✅ SET
   GREEN_CHECK_BASE_URL: ✅ SET
   GREEN_CHECK_ENVIRONMENT: ✅ SET

🎯 Environment variable loading test completed!
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % 