(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-<PERSON><PERSON><PERSON>ep<PERSON> % docker-compose exec api node debug-green-check-app.js
🔍 Debugging Green Check Integration in App Context...

📋 Step 1: Environment Variables
   CLIENT_ID: ❌ Missing
   CLIENT_SECRET: ❌ Missing
   SERVICE_PROVIDER_ID: ❌ Missing
   BASE_URL: https://sandbox-api.greencheckverified.com

🌐 Step 2: Direct API Authentication Test
   Auth URL: https://sandbox-api.greencheckverified.com/auth/token
❌ Direct API authentication failed!
   Status: 422
   Error: Validation Failed
   Response: {
  "message": "Validation Failed",
  "details": {
    "body.client_id": {
      "message": "'client_id' is required"
    },
    "body.client_secret": {
      "message": "'client_secret' is required"
    }
  }
}
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-Mon<PERSON><PERSON><PERSON><PERSON> % docker-compose exec api node test-green-check-provider-simple.js

node:internal/modules/cjs/loader:1143
  throw err;
  ^

Error: Cannot find module '/usr/src/app/apps/platform/test-green-check-provider-simple.js'
    at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
    at Module._load (node:internal/modules/cjs/loader:981:27)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49 {
  code: 'MODULE_NOT_FOUND',
  requireStack: []
}

Node.js v18.20.8
