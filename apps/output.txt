(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker-compose exec api node debug-green-check-app.js
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_RESPONSE_TYPES" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "FIREBASE_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "API_BASE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_FACEBOOK_APP_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] /Users/<USER>/Documents/GitHub/AI-Marketing-Assistant-MonoRepo/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
🔍 Debugging Green Check Integration in App Context...

📋 Step 1: Environment Variables
   CLIENT_ID: ❌ Missing
   CLIENT_SECRET: ❌ Missing
   SERVICE_PROVIDER_ID: ❌ Missing
   BASE_URL: https://sandbox-api.greencheckverified.com

🌐 Step 2: Direct API Authentication Test
   Auth URL: https://sandbox-api.greencheckverified.com/auth/token
❌ Direct API authentication failed!
   Status: 422
   Error: Validation Failed
   Response: {
  "message": "Validation Failed",
  "details": {
    "body.client_id": {
      "message": "'client_id' is required"
    },
    "body.client_secret": {
      "message": "'client_secret' is required"
    }
  }
}
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker-compose exec api env | grep GREEN_CHECK
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_RESPONSE_TYPES" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "FIREBASE_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_FACEBOOK_APP_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "API_BASE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] /Users/<USER>/Documents/GitHub/AI-Marketing-Assistant-MonoRepo/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker-compose exec api ls -la .env
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_RESPONSE_TYPES" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "FIREBASE_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "API_BASE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_FACEBOOK_APP_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] /Users/<USER>/Documents/GitHub/AI-Marketing-Assistant-MonoRepo/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
-rw-r--r-- 1 <USER> <GROUP> 4342 Jul  2 18:26 .env
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker-compose exec api pwd
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "FIREBASE_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_FACEBOOK_APP_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "API_BASE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_RESPONSE_TYPES" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] /Users/<USER>/Documents/GitHub/AI-Marketing-Assistant-MonoRepo/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
/usr/src/app/apps/platform