(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker-compose exec api node test-green-check-provider.js
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "FIREBASE_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "API_BASE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_FACEBOOK_APP_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_RESPONSE_TYPES" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] /Users/<USER>/Documents/GitHub/AI-Marketing-Assistant-MonoRepo/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
🔍 Testing Green Check Provider with Hardcoded Credentials...

❌ All import attempts failed:
   Attempt 1: Cannot find module './build/providers/pos/GreenCheckProvider'
Require stack:
- /usr/src/app/apps/platform/test-green-check-provider.js
   Attempt 2: Cannot find module './build/providers/pos/GreenCheckProvider.js'
Require stack:
- /usr/src/app/apps/platform/test-green-check-provider.js
   Attempt 3: Cannot find module './build/providers/pos/GreenCheckProvider'
Require stack:
- /usr/src/app/apps/platform/test-green-check-provider.js
❌ Failed to import or create GreenCheckProvider: Cannot find module './build/providers/pos/GreenCheckProvider'
Require stack:
- /usr/src/app/apps/platform/test-green-check-provider.js
   Stack: Error: Cannot find module './build/providers/pos/GreenCheckProvider'
Require stack:
- /usr/src/app/apps/platform/test-green-check-provider.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
    at Module._load (node:internal/modules/cjs/loader:981:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at require (node:internal/modules/helpers:177:18)
    at Object.<anonymous> (/usr/src/app/apps/platform/test-green-check-provider.js:17:24)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % 