(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker cp test-env-loading-direct.js $(docker-compose ps -q api):/usr/src/app/apps/platform/
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_RESPONSE_TYPES" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "FIREBASE_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "API_BASE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_FACEBOOK_APP_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] /Users/<USER>/Documents/GitHub/AI-Marketing-Assistant-MonoRepo/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
Successfully copied 5.12kB to ba87f111d1c902dfae30ba723348e6eb20b3b2f5eb982f485ab2793ba621b322:/usr/src/app/apps/platform/
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % docker-compose exec api node test-env-loading-direct.js
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_RESPONSE_TYPES" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "OAUTH_LINKEDIN_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REDIS_TLS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_DRIVER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_ISSUER_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_DEEPLINK_MIRROR_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_ENDPOINT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_FORCE_PATH_STYLE" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SQS_QUEUE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ISSUER" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CERT" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_REDIRECT_URI" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_ENTRY_POINT_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_IS_AUTHN_SIGNED" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_SENTRY_DSN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "FIREBASE_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_OPENID_DOMAIN_WHITELIST" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STORAGE_S3_BUCKET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_NAME" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_GOOGLE_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "ERROR_BUGSNAG_API_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_FIREBASE_CREDENTIALS" variable is not set. Defaulting to a blank string. 
WARN[0000] The "STRIPE_WEBHOOK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "TRACKING_LINK_WRAP" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_WORKSPACE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_SQL_WAREHOUSE_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_ACCESS_KEY_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_SECRET_ACCESS_KEY" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AUTH_SAML_CALLBACK_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "API_BASE_URL" variable is not set. Defaulting to a blank string. 
WARN[0000] The "AWS_REGION" variable is not set. Defaulting to a blank string. 
WARN[0000] The "SUPABASE_API_TOKEN" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_FACEBOOK_APP_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "REACT_APP_LINKEDIN_CLIENT_ID" variable is not set. Defaulting to a blank string. 
WARN[0000] The "APP_CHECK_SECRET" variable is not set. Defaulting to a blank string. 
WARN[0000] /Users/<USER>/Documents/GitHub/AI-Marketing-Assistant-MonoRepo/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
WARN[0000] The "pA76dRw" variable is not set. Defaulting to a blank string. 
🔍 Direct Environment Variable Loading Test...

📂 Current working directory: /usr/src/app/apps/platform

📁 Checking .env file existence:
   .env: ✅ EXISTS
      Contains GREEN_CHECK vars: ✅ YES
      GREEN_CHECK lines found: 5
   /usr/src/app/.env: ❌ MISSING
   /usr/src/app/apps/platform/.env: ✅ EXISTS
      Contains GREEN_CHECK vars: ✅ YES
      GREEN_CHECK lines found: 5
   ../../.env: ❌ MISSING

🔧 Current Environment Variables:
   GREEN_CHECK_CLIENT_ID: ❌ MISSING
   GREEN_CHECK_CLIENT_SECRET: ❌ MISSING
   GREEN_CHECK_SERVICE_PROVIDER_ID: ❌ MISSING
   GREEN_CHECK_BASE_URL: ❌ MISSING
   GREEN_CHECK_ENVIRONMENT: ❌ MISSING

🔄 Manual dotenv loading:
   .env: ✅ LOADED
   /usr/src/app/apps/platform/.env: ✅ LOADED

🔍 Environment Variables after manual loading:
   GREEN_CHECK_CLIENT_ID: ✅ SET
      Value: 5fad96d5-4842-4377-ad48-57f7db6b8b2c
   GREEN_CHECK_CLIENT_SECRET: ✅ SET
   GREEN_CHECK_SERVICE_PROVIDER_ID: ✅ SET
      Value: f2b48e57-7114-432d-8f11-a0d74b8fb934
   GREEN_CHECK_BASE_URL: ✅ SET
      Value: https://sandbox-api.greencheckverified.com
   GREEN_CHECK_ENVIRONMENT: ✅ SET
      Value: sandbox

🧪 Testing direct credential setting:
✅ Credentials set directly in process.env
   GREEN_CHECK_CLIENT_ID: ✅ SET
   GREEN_CHECK_CLIENT_SECRET: ✅ SET
   GREEN_CHECK_SERVICE_PROVIDER_ID: ✅ SET
   GREEN_CHECK_BASE_URL: ✅ SET
   GREEN_CHECK_ENVIRONMENT: ✅ SET

🎯 Environment variable loading test completed!
(.venv) (base) maungthura2024@Maungs-MacBook-Pro-4 AI-Marketing-Assistant-MonoRepo % 