import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import Button from "../../ui/Button";
import { Location } from "../../types";
import toastr from "toastr";
import { DataSource } from "../../views/auth/OnboardingLocation";
import POSConnectionDialog from "../dialogs/POSConnectionDialog";
import GreenCheckIntegration from "./GreenCheckIntegration";
import DutchieIntegration from "./DutchieIntegration";

const SUPPORTED_POS_SYSTEMS = [
  {
    id: "manual",
    name: "Manual upload",
    available: true,
    apiKey: "",
    siteId: "",
    connected: false,
    popularity: 99, // Should not appear in the grid
    subtitle: "Upload files from any system",
  },
  {
    id: "greencheck",
    name: "Green Check",
    available: true,
    apiKey: "",
    siteId: "",
    connected: false,
    isUnified: true,
    popularity: 1, // Recommended option
    subtitle: "One login for all systems",
  },
  {
    id: "MJS",
    name: "Marijuana Software",
    available: true,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 2,
    subtitle: "Direct integration",
  },
  {
    id: "dutchie",
    name: "Dutchie",
    available: true,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 3,
    subtitle: "Most popular POS",
  },
  {
    id: "treez",
    name: "Treez",
    available: false,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 4,
    subtitle: "Enterprise-focused",
  },
  {
    id: "flowhub",
    name: "Flowhub",
    available: false,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 5,
    subtitle: "Fastest-growing POS",
  },
  {
    id: "blaze",
    name: "BLAZE",
    available: false,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 6,
    subtitle: "All-in-one platform",
  },
  {
    id: "greenbits",
    name: "Greenbits",
    available: false,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 7,
    subtitle: "Trusted by retailers",
  },
  {
    id: "cova",
    name: "Cova",
    available: false,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 8,
    subtitle: "Canadian leader",
  },
  {
    id: "meadow",
    name: "Meadow",
    available: false,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 9,
    subtitle: "Modern interface",
  },
  {
    id: "sweed",
    name: "Sweed",
    available: false,
    apiKey: "",
    siteId: "",
    connected: false,
    isDirect: true,
    popularity: 10,
    subtitle: "Compliance-first",
  },
];

export interface DataCollected {
  manual: boolean;
  pos: boolean;
}

interface DataSourcesStepProps {
  selectedBusiness: Partial<Location>;
  onDataSourceConnect: (
    type: "pos" | "customer_data",
    source: DataSource
  ) => void;
  onStepComplete: () => void;
  canSkip: boolean;
  documents: File[];
  setDocuments: (files: File[]) => void;
  setCustomerData: (files: File[]) => void;
  customerData: File[];
  isCompleted: boolean;
  dataSources: Set<DataSource>;
  setDataSources: (sources: Set<DataSource>) => void;
  onBack?: () => void;
  setChangeToManualUpload: (x: boolean) => void;
  changeToManualUpload: boolean;
  setDataCollected: (x: DataCollected) => void;
  dataCollected: DataCollected;
  onBusinessDocumentsUploaded?: (hasDocuments: boolean) => void;
}

const DataSourcesStep: React.FC<DataSourcesStepProps> = ({
  selectedBusiness,
  onDataSourceConnect,
  setCustomerData,
  customerData,
  documents,
  setDocuments,
  onStepComplete,
  canSkip,
  isCompleted,
  changeToManualUpload,
  setChangeToManualUpload,
  onBack,
  setDataCollected,
  dataCollected,
  onBusinessDocumentsUploaded,
}) => {
  const { t } = useTranslation();
  const [selectedDataSource, setSelectedDataSource] = React.useState<
    string | null
  >(null);
  const [isConnecting, setIsConnecting] = React.useState(false);
  const [availablePOSSystems, setAvailablePOSSystems] = React.useState(
    SUPPORTED_POS_SYSTEMS
  );
  const [showPOSDialog, setShowPOSDialog] = useState(false);
  const [posId, setPosId] = React.useState("");
  const [posName, setPosName] = useState("");
  const [showGreenCheckDialog, setShowGreenCheckDialog] = useState(false);
  const [showDutchieDialog, setShowDutchieDialog] = useState(false);

  useEffect(() => {
    // When documents are uploaded, notify parent to mark business-documents as completed
    if (documents.length > 0 && onBusinessDocumentsUploaded) {
      onBusinessDocumentsUploaded(true);
    }
  }, [documents, onBusinessDocumentsUploaded]);

  const handleConnectPOS = async (apiKey: string, siteId: string) => {
    setIsConnecting(true);
    setSelectedDataSource(posId);

    // Simulate API connection with a timeout
    setTimeout(() => {
      try {
        // Find the selected POS system
        const posSystem = availablePOSSystems.find((p) => p.id === posId);

        if (!posSystem) {
          console.error("POS system not found:", posId);
          setIsConnecting(false);
          return;
        }

        // Update localStorage
        localStorage.setItem(
          "onboarding_data_sources",
          JSON.stringify({
            pos: posSystem.name || posId,
            timestamp: new Date().toISOString(),
          })
        );

        // Update POS system details
        setAvailablePOSSystems((prevSystems) =>
          prevSystems.map((p) =>
            p.id === posId ? { ...p, apiKey, siteId, connected: true } : p
          )
        );

        // Notify parent component
        onDataSourceConnect("pos", {
          ...posSystem,
          apiKey,
          siteId,
          connected: true,
        } as DataSource);

        setDataCollected({ manual: documents.length > 0, pos: true });
      } catch (error) {
        console.error("POS connection failed:", error);
      } finally {
        setIsConnecting(false);
        setShowPOSDialog(false);
      }
    }, 1000);
  };

  const handleGreenCheckConnection = (crbId: string, crbInfo: any) => {
    // Update the Green Check POS system as connected
    setAvailablePOSSystems((prevSystems) =>
      prevSystems.map((p) =>
        p.id === "greencheck"
          ? {
              ...p,
              connected: true,
              apiKey: crbId, // Store CRB ID as apiKey for now
              siteId: crbInfo.name,
            }
          : p
      )
    );

    // Notify parent component
    onDataSourceConnect("pos", {
      id: "greencheck",
      name: "Green Check",
      available: true,
      connected: true,
      apiKey: crbId, // For backward compatibility
      siteId: crbInfo.name,
      crbId: crbId, // Proper CRB ID field
    } as DataSource);

    setDataCollected({ manual: documents.length > 0, pos: true });
    setShowGreenCheckDialog(false);
  };

  const handleGreenCheckError = (error: string) => {
    console.error("Green Check connection error:", error);
    setShowGreenCheckDialog(false);
  };

  const handleDutchieConnection = (apiKey: string, locationInfo: any) => {
    // Update the Dutchie POS system as connected
    setAvailablePOSSystems((prevSystems) =>
      prevSystems.map((p) =>
        p.id === "dutchie"
          ? {
              ...p,
              connected: true,
              apiKey: apiKey,
              siteId: locationInfo.locationName,
            }
          : p
      )
    );

    // Notify parent component
    onDataSourceConnect("pos", {
      id: "dutchie",
      name: "Dutchie",
      available: true,
      connected: true,
      apiKey: apiKey,
      siteId: locationInfo.locationName,
      locationInfo: locationInfo,
    } as DataSource);

    setDataCollected({ manual: documents.length > 0, pos: true });
    setShowDutchieDialog(false);
  };

  const handleDutchieError = (error: string) => {
    console.error("Dutchie connection error:", error);
    setShowDutchieDialog(false);
  };

  const handleManualUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
    type: "pos" | "documents"
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      if (type === "documents") {
        setDocuments([...documents, file]);
        // Notify parent when business documents are uploaded
        if (onBusinessDocumentsUploaded) {
          onBusinessDocumentsUploaded(true);
        }
      } else {
        setCustomerData([...customerData, file]);
      }

      if (type === "pos") {
        setDataCollected({
          pos: availablePOSSystems.some((pos) => pos.connected) || true,
          manual: documents.length > 0,
        });
      } else {
        setDataCollected({
          pos:
            availablePOSSystems.some((pos) => pos.connected) ||
            customerData.length > 0,
          manual: true,
        });
      }
    }
  };

  function handleUpload(type: "pos" | "documents") {
    if (type === "pos") {
      document.getElementById("dataSource")?.click();
    } else {
      document.getElementById("dataSource1")?.click();
    }
  }

  // Handle File Drop
  function handleDrop(
    event: React.DragEvent<HTMLDivElement>,
    type: "pos" | "documents"
  ) {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file) {
      if (type === "documents") {
        setDocuments([...documents, file]);
        // Notify parent when business documents are dropped
        if (onBusinessDocumentsUploaded) {
          onBusinessDocumentsUploaded(true);
        }
      } else {
        setCustomerData([...customerData, file]);
      }

      if (type === "pos") {
        setDataCollected({
          pos: availablePOSSystems.some((pos) => pos.connected) || true,
          manual: documents.length > 0,
        });
      } else {
        setDataCollected({
          pos:
            availablePOSSystems.some((pos) => pos.connected) ||
            customerData.length > 0,
          manual: true,
        });
      }
    }
  }

  // Prevent default drag behaviors
  function handleDragOver(event: React.DragEvent<HTMLDivElement>) {
    event.preventDefault();
  }

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  const handleDisconnectPOS = (posId: string) => {
    // Update available POS systems
    setAvailablePOSSystems((prev) =>
      prev.map((pos) =>
        pos.id === posId
          ? { ...pos, apiKey: "", siteId: "", connected: false }
          : pos
      )
    );

    // Update data collected state
    const stillHasPosConnection = availablePOSSystems.some(
      (pos) => pos.id !== posId && pos.connected
    );

    setDataCollected({
      pos: stillHasPosConnection,
      manual: customerData.length > 0 || documents.length > 0,
    });

    // Notify parent that POS has been disconnected by calling onDataSourceConnect with connected=false
    const disconnectedPOS = availablePOSSystems.find((p) => p.id === posId);
    if (disconnectedPOS) {
      onDataSourceConnect("pos", {
        ...disconnectedPOS,
        connected: false,
        apiKey: "",
        siteId: "",
      });
    }
  };

  const handleDeleteFile = (fileIndex: number, type: "pos" | "documents") => {
    if (type === "pos") {
      const newFiles = customerData.filter((_, index) => index !== fileIndex);
      setCustomerData(newFiles);

      // Update data collected state
      const posConnected = availablePOSSystems.some((pos) => pos.connected);
      setDataCollected({
        pos: posConnected || newFiles.length > 0,
        manual: documents.length > 0,
      });

      // If all POS files are removed and no POS is connected, notify parent
      if (newFiles.length === 0 && !posConnected) {
        // Call with empty data source to indicate removal
        onDataSourceConnect("pos", {
          id: "manual",
          name: "Manual upload",
          available: true,
          connected: false,
        });
      }
    } else {
      const newFiles = documents.filter((_, index) => index !== fileIndex);
      setDocuments(newFiles);

      // Update data collected state
      setDataCollected({
        pos:
          availablePOSSystems.some((pos) => pos.connected) ||
          customerData.length > 0,
        manual: newFiles.length > 0,
      });

      // If all documents are removed, notify parent that business documents are gone
      if (newFiles.length === 0 && onBusinessDocumentsUploaded) {
        onBusinessDocumentsUploaded(false);
      }
    }
  };

  return (
    <div>
      {/* Header section */}
      <div className="flex justify-between items-start mb-4">
        <div className="mr-4">
          <h2 className="text-xl font-semibold text-primary mb-1 mt-0">
            Connect Your Data
          </h2>
          <p className="text-primary-soft m-0 text-sm">
            Connect your POS system and upload business documents
          </p>
        </div>

        {/* Upgrade Plan */}
        <div className="flex-shrink-0 bg-primary bg-opacity-10 rounded-lg p-3 shadow-sm border border-primary border-opacity-30">
          <div className="mb-2">
            <h3 className="text-sm font-semibold text-primary-soft">
              Upgrade Required
            </h3>
            <p className="text-xs text-primary-soft">
              Growth plan at $99/month
            </p>
          </div>
          <div className="flex items-center justify-between">
            <div className="bg-green-100 text-green-800 text-xs py-0.5 px-1.5 rounded">
              Recommended
            </div>
            <Button
              variant="primary"
              onClick={() => window.open("/upgrade", "_blank")}
              className="whitespace-nowrap text-xs"
            >
              View Plans
            </Button>
          </div>
        </div>
      </div>

      {/* Two-column layout for POS Integration and Document Upload */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
        {/* POS Integration Section */}
        <div className="bg-surface border border-divider rounded-lg p-4 shadow-sm md:col-span-3">
          <h3 className="text-lg font-medium mb-3">
            Point of Sale Integration
          </h3>

          <div className="grid grid-cols-3 gap-2 mb-3">
            {availablePOSSystems
              .filter((pos) => pos.id !== "manual")
              .sort((a, b) => a.popularity - b.popularity)
              .slice(0, 9)
              .map((posSystem) => {
                const isGreenCheck = posSystem.id === "greencheck";
                const isConnected = posSystem.connected;

                let tileClasses =
                  "border rounded-lg p-2 text-center relative overflow-hidden transition-all h-full flex flex-col justify-between";

                if (isGreenCheck) {
                  tileClasses += " border-2 border-green-200 bg-green-50/50";
                } else if (posSystem.available) {
                  tileClasses +=
                    " border-divider hover:border-primary cursor-pointer";
                } else {
                  tileClasses += " border-divider opacity-60";
                }

                return (
                  <div key={posSystem.id} className={tileClasses}>
                    <div>
                      {isGreenCheck && (
                        <div className="absolute -top-1 -right-1 w-12 h-6">
                          <div className="w-full h-full bg-green-600 transform flex items-center justify-center">
                            <span className="text-white text-xs font-medium transform">
                              ⚡
                            </span>
                          </div>
                        </div>
                      )}

                      <div className="flex justify-center mb-1">
                        <div
                          className={`w-8 h-8 rounded-md flex items-center justify-center ${
                            isGreenCheck
                              ? "bg-green-100"
                              : "bg-surface-secondary"
                          }`}
                        >
                          <span
                            className={`text-xs font-semibold ${
                              isGreenCheck
                                ? "text-green-700"
                                : "text-primary-soft"
                            }`}
                          >
                            {isGreenCheck ? "GC" : posSystem.name.charAt(0)}
                          </span>
                        </div>
                      </div>

                      <div className="mb-2">
                        <span
                          className={`text-xs font-medium line-clamp-1 ${
                            isGreenCheck
                              ? "text-green-900"
                              : "text-primary-soft"
                          }`}
                        >
                          {posSystem.name}
                        </span>
                        <p
                          className={`text-xs text-primary-soft mt-1 ${
                            isGreenCheck ? "text-green-700" : ""
                          }`}
                        >
                          {posSystem.subtitle}
                        </p>
                        {isConnected && (
                          <div
                            className={`text-xs mt-1 flex items-center justify-center ${
                              isGreenCheck ? "text-green-600" : "text-[#3EDC81]"
                            }`}
                          >
                            <svg
                              className="w-3 h-3 mr-1"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            Connected
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="mt-auto">
                      {posSystem.available ? (
                        isConnected ? (
                          <Button
                            onClick={() => handleDisconnectPOS(posSystem.id)}
                            variant="destructive"
                            className="text-xs py-1 px-2 w-full"
                          >
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            onClick={() => {
                              if (isGreenCheck) {
                                setShowGreenCheckDialog(true);
                              } else if (posSystem.id === "dutchie") {
                                setShowDutchieDialog(true);
                              } else {
                                setPosId(posSystem.id);
                                setPosName(posSystem.name);
                                setShowPOSDialog(true);
                              }
                            }}
                            variant={isGreenCheck ? "primary" : "secondary"}
                            className={`text-xs py-1 px-2 w-full ${
                              isGreenCheck
                                ? "bg-green-600 hover:bg-green-700"
                                : ""
                            }`}
                            disabled={isConnecting}
                          >
                            Connect
                          </Button>
                        )
                      ) : (
                        <span className="text-xs bg-surface-secondary text-primary-soft px-2 py-0.5 rounded">
                          Soon
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
          </div>

          {/* Manual upload section */}
          <div className="border-t border-divider pt-3 mt-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-primary-soft">
                Don't see your system?
              </span>
              <Button
                onClick={() => handleUpload("pos")}
                variant="secondary"
                className="text-xs py-1 px-3"
              >
                Upload Files
              </Button>
            </div>
            <input
              id="dataSource"
              type="file"
              style={{ display: "none" }}
              onChange={(e) => handleManualUpload(e, "pos")}
            />
          </div>

          {/* Show uploaded files if any */}
          {customerData.length > 0 && (
            <div className="mt-2">
              <h4 className="text-xs font-medium text-primary-soft mb-1">
                Uploaded Files:
              </h4>
              <div className="max-h-[80px] overflow-y-auto">
                {customerData.map((file, index) => (
                  <div
                    className="text-xs flex items-center justify-between py-1"
                    key={index}
                  >
                    <span className="flex items-center">
                      <svg
                        className="w-3 h-3 mr-1 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      {file.name}
                    </span>
                    <button
                      onClick={() => handleDeleteFile(index, "pos")}
                      className="text-red-500 hover:text-red-700 transition-colors"
                    >
                      <svg
                        className="w-3.5 h-3.5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Document Upload Section */}
        <div className="bg-surface border border-divider rounded-lg p-4 shadow-sm md:col-span-2">
          <h3 className="text-lg font-medium mb-2">Business Documents</h3>

          <p className="text-sm text-primary-soft mb-2">
            Upload licenses, permits, or other documentation
          </p>

          <p className="text-xs text-primary-soft mb-3">
            Save up to 15 hours/month on compliance
          </p>

          <div
            className="border-2 border-dashed border-divider rounded-lg p-4 text-center hover:bg-surface-secondary transition-colors cursor-pointer"
            onDrop={(e) => handleDrop(e, "documents")}
            onDragOver={handleDragOver}
            onClick={() => handleUpload("documents")}
          >
            <div className="flex flex-col items-center">
              <svg
                className="w-8 h-8 mb-2 text-primary-soft"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
              <p className="text-sm font-medium mb-1">Drop files or click</p>
              <Button variant="secondary" className="text-sm">
                Browse Files
              </Button>
              <p className="text-xs text-primary-soft mt-1">PDF, CSV, XLSX</p>
              <input
                id="dataSource1"
                type="file"
                style={{ display: "none" }}
                onChange={(e) => handleManualUpload(e, "documents")}
              />
            </div>
          </div>

          {documents.length > 0 && (
            <div className="mt-2 border-t border-divider pt-2">
              <h4 className="text-xs font-medium text-primary-soft mb-1">
                Uploaded Files:
              </h4>
              <div className="max-h-[100px] overflow-y-auto">
                {documents.map((file, index) => (
                  <div
                    className="text-xs flex items-center justify-between py-1"
                    key={index}
                  >
                    <span className="flex items-center">
                      <svg
                        className="w-3 h-3 mr-1 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      {file.name}
                    </span>
                    <button
                      onClick={() => handleDeleteFile(index, "documents")}
                      className="text-red-500 hover:text-red-700 transition-colors"
                    >
                      <svg
                        className="w-3.5 h-3.5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-between mt-4 pt-2 border-t border-divider">
        <Button onClick={handleBack} variant="secondary" className="mr-4">
          {t("onboarding.navigation.back")}
        </Button>
        <div className="flex">
          {dataCollected.manual === false && dataCollected.pos === false && (
            <Button
              onClick={onStepComplete}
              variant="secondary"
              className="mr-4"
            >
              {t("skip_for_now")}
            </Button>
          )}
          <Button
            onClick={onStepComplete}
            variant="primary"
            disabled={!dataCollected.manual && !dataCollected.pos}
          >
            {t("onboarding.navigation.continue")}
          </Button>
        </div>
      </div>

      {/* POSConnectionDialog component */}
      <POSConnectionDialog
        open={showPOSDialog}
        onClose={() => setShowPOSDialog(false)}
        onConnect={handleConnectPOS}
        posId={posId}
        posName={posName}
        isConnecting={isConnecting}
      />

      {/* Green Check Integration Dialog */}
      {showGreenCheckDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-surface border border-divider rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-lg font-medium">Green Check Integration</h2>
                <p className="text-sm text-gray-400 mt-1">
                  Connect securely to multiple POS systems with one login
                </p>
              </div>
              <button
                onClick={() => setShowGreenCheckDialog(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <GreenCheckIntegration
              onConnectionSuccess={handleGreenCheckConnection}
              onConnectionError={handleGreenCheckError}
              onCancel={() => setShowGreenCheckDialog(false)}
            />
          </div>
        </div>
      )}

      {/* Dutchie Integration Dialog */}
      {showDutchieDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-surface border border-divider rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-lg font-medium">Dutchie Integration</h2>
                <p className="text-sm text-gray-400 mt-1">
                  Connect directly to your Dutchie POS system
                </p>
              </div>
              <button
                onClick={() => setShowDutchieDialog(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <DutchieIntegration
              onConnectionSuccess={handleDutchieConnection}
              onConnectionError={handleDutchieError}
              onCancel={() => setShowDutchieDialog(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default DataSourcesStep;
