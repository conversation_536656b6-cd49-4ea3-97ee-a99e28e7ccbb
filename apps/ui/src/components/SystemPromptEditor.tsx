import React, { useState, useEffect } from "react";
import Button from "../ui/Button";
import axios from "axios";
import { useParams, useLocation } from "react-router-dom";
import api from "../api";

// Create an API service
const apiService = {
  get: (url: string) => axios.get(url),
  post: (url: string, data?: any) => axios.post(url, data),
  put: (url: string, data?: any) => axios.put(url, data),
  delete: (url: string) => axios.delete(url),
};

interface SystemPrompt {
  id: number;
  name: string;
  content: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface SystemPromptEditorProps {
  onClose?: () => void;
}

const SystemPromptEditor: React.FC<SystemPromptEditorProps> = ({ onClose }) => {
  const [prompts, setPrompts] = useState<SystemPrompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingPrompt, setEditingPrompt] = useState<SystemPrompt | null>(null);
  const [name, setName] = useState("");
  const [content, setContent] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [isCreating, setIsCreating] = useState(false);

  // Get location ID from URL
  const { id: locationId } = useParams<{ id: string }>();
  const location = useLocation();

  // Extract location ID from path if not in params
  const getLocationId = (): string => {
    if (locationId) return locationId;

    // Extract from path like /locations/123/settings/chat
    const matches = location.pathname.match(/\/locations\/(\d+)/);
    return matches && matches[1] ? matches[1] : "";
  };

  const currentLocationId = getLocationId();

  // Fetch prompts on component mount
  useEffect(() => {
    if (currentLocationId) {
      fetchPrompts();
    } else {
      setError("Location ID not found in the URL");
      setLoading(false);
    }
  }, [currentLocationId]);

  const fetchPrompts = async () => {
    try {
      setLoading(true);
      const data = await api.customPrompts.list(currentLocationId);
      setPrompts(data);
      setError(null);
    } catch (err) {
      setError("Failed to load custom prompts");
      console.error("Error fetching prompts:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePrompt = () => {
    setIsCreating(true);
    setEditingPrompt(null);
    setName("");
    setContent("");
    setIsActive(true);
  };

  const handleEditPrompt = (prompt: SystemPrompt) => {
    setIsCreating(false);
    setEditingPrompt(prompt);
    setName(prompt.name);
    setContent(prompt.content);
    setIsActive(prompt.is_active);
  };

  const handleTogglePrompt = async (prompt: SystemPrompt) => {
    try {
      await api.customPrompts.toggle(currentLocationId, prompt.id);
      await fetchPrompts(); // Refresh the list
    } catch (err) {
      setError("Failed to toggle prompt status");
      console.error("Error toggling prompt:", err);
    }
  };

  const handleSave = async () => {
    try {
      const promptData = {
        name,
        content,
        is_active: isActive,
      };

      if (isCreating) {
        await api.customPrompts.create(currentLocationId, promptData);
      } else if (editingPrompt) {
        await api.customPrompts.update(
          currentLocationId,
          editingPrompt.id,
          promptData
        );
      }

      // Reset form and refresh list
      setIsCreating(false);
      setEditingPrompt(null);
      setName("");
      setContent("");
      setIsActive(true);
      await fetchPrompts();
    } catch (err) {
      setError("Failed to save prompt");
      console.error("Error saving prompt:", err);
    }
  };

  const handleCancel = () => {
    setIsCreating(false);
    setEditingPrompt(null);
    setName("");
    setContent("");
    setIsActive(true);
  };

  const handleDelete = async (promptId: number) => {
    if (window.confirm("Are you sure you want to delete this prompt?")) {
      try {
        await api.customPrompts.delete(currentLocationId, promptId);
        await fetchPrompts(); // Refresh the list
      } catch (err) {
        setError("Failed to delete prompt");
        console.error("Error deleting prompt:", err);
      }
    }
  };

  if (loading && prompts.length === 0) {
    return <div className="p-4">Loading system prompts...</div>;
  }

  return (
    <div className="p-4 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Custom System Prompts</h2>
        {onClose && (
          <Button onClick={onClose} className="text-sm" variant="secondary">
            Close
          </Button>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4">
          {error}
        </div>
      )}

      {/* Editor form */}
      {(isCreating || editingPrompt) && (
        <div className="border rounded p-4 mb-4">
          <h3 className="font-medium mb-2">
            {isCreating ? "Create New Prompt" : "Edit Prompt"}
          </h3>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border rounded"
              placeholder="Enter prompt name"
            />
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Content</label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full px-3 py-2 border rounded h-40 font-mono text-sm"
              placeholder="Enter system prompt content"
            />
          </div>
          <div className="mb-3 flex items-center">
            <input
              type="checkbox"
              id="is-active"
              checked={isActive}
              onChange={(e) => setIsActive(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="is-active" className="text-sm">
              Active
            </label>
          </div>
          <div className="flex space-x-2">
            <Button onClick={handleSave} className="text-sm">
              Save
            </Button>
            <Button
              onClick={handleCancel}
              className="text-sm"
              variant="secondary"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Create prompt button */}
      {!isCreating && !editingPrompt && (
        <Button onClick={handleCreatePrompt} className="mb-4">
          Create New Prompt
        </Button>
      )}

      {/* Prompts list */}
      {prompts.length === 0 ? (
        <div className="text-gray-500 text-center py-4">
          No custom prompts found. Create one to get started.
        </div>
      ) : (
        <div className="space-y-3">
          {prompts.map((prompt) => (
            <div
              key={prompt.id}
              className={`border rounded p-3 ${
                prompt.is_active ? "border-green-400" : "border-gray-300"
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-medium">{prompt.name}</h3>
                  <p className="text-xs text-gray-500">
                    Updated: {new Date(prompt.updated_at).toLocaleString()}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleTogglePrompt(prompt)}
                    className={`px-2 py-1 text-xs rounded ${
                      prompt.is_active
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {prompt.is_active ? "Active" : "Inactive"}
                  </button>
                  <button
                    onClick={() => handleEditPrompt(prompt)}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(prompt.id)}
                    className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded"
                  >
                    Delete
                  </button>
                </div>
              </div>
              <div className="text-sm p-2 rounded font-mono whitespace-pre-wrap max-h-24 overflow-y-auto">
                {prompt.content.substring(0, 150)}
                {prompt.content.length > 150 && "..."}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SystemPromptEditor;
