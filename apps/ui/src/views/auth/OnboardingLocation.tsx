import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useEffect, useState, useRef, useContext } from "react";
import {
  Competitor,
  Location,
  PosConnectionResult,
  SupabaseUploadResult,
} from "../../types";
import { getToken } from "firebase/app-check";
import { appCheck } from "../../config/firebase";
import { toast } from "react-hot-toast";
import api from "../../api";
import { pushRecentLocation } from "../../utils";
import type { Agent as AgentType } from "../../components/agents/AgentCard";
import AgentCard from "../../components/agents/AgentCard";
import CompactAgentCard from "../../components/agents/CompactAgentCard";
import {
  searchNearbyRetailers,
  RetailerResult,
  validateCoordinates,
} from "../../services/firestore";
import { miscService } from "../../api/misc";
import { AdminContext } from "../../contexts";

// Component Imports
import StepProgress from "../../components/onboarding/StepProgress";
import CompanyInfoStep from "../../components/onboarding/CompanyInfoStep";
import DataSourcesStep, {
  DataCollected,
} from "../../components/onboarding/DataSourcesStep";
import CompetitorsStep from "../../components/onboarding/CompetitorsStep";
import SocialMediaStep from "../../components/onboarding/SocialMediaStep";
import { SocialPlatform } from "../../components/onboarding/SocialMediaStep";
import SkipDialog from "../../components/dialogs/SkipDialog";
import AIPredictionToast from "../../components/onboarding/AIPredictionToast";
import ScheduleCallModal from "../../components/onboarding/ScheduleCallModal";

type OnboardingStep =
  | "company-info"
  | "data-sources"
  | "competitors"
  | "business-documents"
  | "social-media";

declare global {
  interface Window {
    initMap: () => void;
    google: any;
  }
}

interface Agent {
  id: string;
  name: string;
  role: string;
  description: string;
  examples: string[];
  requirements: {
    required: string[];
    optional: string[];
  };
  icon: string;
  unlockCondition: (
    completedSteps: Set<string>,
    connectedData: Set<string>
  ) => "locked" | "partial" | "unlocked";
  disabled?: boolean;
}

export interface DataSource {
  name: string;
  id: string;
  available: boolean;
  connected?: boolean;
  apiKey?: string;
  siteId?: string;
  crbId?: string; // For Green Check CRB ID
}

interface AgentContribution {
  agent_id: string;
  relevance_score: number;
  key_insights: string[];
}

const agents: AgentType[] = [
  {
    id: "smokey",
    name: "SMOKEY",
    role: "AI Budtender & Customer Experience",
    description:
      "Customer-facing AI assistant for product recommendations and education",
    examples: [
      "What's the best strain for relaxation?",
      "Can you explain the effects of this edible?",
      "What's the THC content of this product?",
      "Are there any discounts on CBD oils today?",
    ],
    requirements: {
      required: ["company-info"],
      optional: ["pos"],
    },
    icon: "user",
    unlockCondition: (steps, data) => {
      if (data.has("company-info")) return "partial";
      if (data.has("pos") && data.has("company-info")) return "unlocked";
      return "locked";
    },
  },
  {
    id: "craig",
    name: "CRAIG",
    role: "Marketing Automation",
    description: "Marketing strategy and campaign management",
    examples: [
      "Create a marketing campaign for our new CBD line",
      "What were our top-selling products last month?",
      "Generate a compliant email for our loyalty program members",
      "Analyze the performance of our last social media campaign",
    ],
    requirements: {
      required: ["pos", "company-info"],
      optional: ["social-media"],
    },
    icon: "megaphone",
    unlockCondition: (steps, data) => {
      if (data.has("pos") && steps.has("social-media")) return "unlocked";
      if (data.has("pos")) return "partial";
      return "locked";
    },
  },
  {
    id: "pops",
    name: "POPS",
    role: "Business Intelligence & Strategy",
    description: "Sales performance analysis and operational efficiency",
    examples: [
      "Analyze top-selling products this quarter",
      "Suggest staff scheduling optimizations",
    ],
    requirements: {
      required: ["pos", "company-info"],
      optional: ["business-documents"],
    },
    icon: "chart",
    unlockCondition: (steps, data) => {
      if (data.has("pos") && steps.has("business-documents")) return "unlocked";
      if (data.has("pos")) return "partial";
      return "locked";
    },
  },
  {
    id: "ezal",
    name: "EZAL",
    role: "Market Intelligence",
    description: "Market analysis and competitive intelligence",
    examples: [
      "How do our prices compare to competitors?",
      "What are the emerging trends in our market?",
      "Analyze our market share for edibles",
      "Which products are underperforming compared to the market?",
    ],
    requirements: {
      required: ["competitors", "company-info"],
      optional: [],
    },
    icon: "search",
    unlockCondition: (steps, data) =>
      steps.has("competitors") && steps.has("company-info")
        ? "unlocked"
        : steps.has("competitors")
        ? "partial"
        : "locked",
  },
  {
    id: "money-mike",
    name: "MONEY MIKE",
    role: "Financial Analytics",
    description: "Profit margin analysis and financial planning",
    examples: [
      "Calculate profit margins by category",
      "Forecast revenue for next quarter",
    ],
    requirements: {
      required: ["business-documents", "company-info"],
      optional: [],
    },
    icon: "dollar",
    unlockCondition: (steps, data) =>
      steps.has("business-documents") && steps.has("company-info")
        ? "unlocked"
        : steps.has("business-documents")
        ? "partial"
        : "locked",
  },
  {
    id: "mrs-parker",
    name: "MRS. PARKER",
    role: "Customer Relations",
    description: "VIP customer management and loyalty program optimization",
    examples: [
      "Who are our top 10 customers by purchase volume?",
      "Generate a personalized offer for customer #12345",
      "What's the redemption rate of our loyalty program?",
      "Analyze customer preferences for indica vs sativa strains",
    ],
    requirements: {
      required: ["pos", "company-info", "social-media"],
      optional: [],
    },
    icon: "users",
    unlockCondition: (steps, data) =>
      data.has("pos") && steps.has("company-info") && steps.has("social-media")
        ? "unlocked"
        : data.has("pos") && steps.has("company-info")
        ? "partial"
        : "locked",
  },
  {
    id: "deebo",
    name: "DEEBO",
    role: "Compliance monitoring",
    description:
      "Compliance monitoring, security management, and quality assurance",
    examples: [
      "Are we compliant with the latest regulations?",
      "Generate a security protocol for our new location",
      "What licenses are up for renewal in the next 30 days?",
      "Perform a risk assessment for our delivery service",
      "What are the latest test results for Batch #1234?",
      "Generate a Certificate of Analysis for our new strain",
    ],
    requirements: {
      required: ["government-database", "lab-data", "company-info"],
      optional: [],
    },
    icon: "shield",
    unlockCondition: (steps, data) => {
      if (steps.has("company-info")) return "partial";
      return "locked";
    },
  },
  {
    id: "day-day",
    name: "DAY-DAY (Coming Soon)",
    role: "Seed-to-Sale & Logistics Specialist",
    description:
      "End-to-end cultivation, processing tracking, and logistics management",
    examples: [
      "What's the current status of Batch #4269?",
      "When is the next harvest scheduled?",
      "Calculate the yield from last month's harvest",
    ],
    requirements: {
      required: [],
      optional: [],
    },
    icon: "sprout",
    unlockCondition: () => "locked" as const,
    disabled: true,
  },
  {
    id: "big-worm",
    name: "BIG WORM (Coming Soon)",
    role: "Supply Chain",
    description: "Supply chain and inventory management",
    examples: [
      "What's our current inventory of OG Kush?",
      "Create a purchase order for our top-selling products",
      "Forecast inventory needs for next month",
    ],
    requirements: {
      required: [],
      optional: [],
    },
    icon: "box",
    unlockCondition: () => "locked" as const,
    disabled: true,
  },
];

// New QuickStartProgress component for the simplified flow
const QuickStartProgress = ({ currentStep }: { currentStep: string }) => {
  return (
    <div className="mb-3 sm:mb-4">
      <div className="flex items-center justify-center">
        <div className="flex items-center space-x-3 bg-surface-secondary rounded-full py-2.5 px-5 border border-border">
          {currentStep === "company-info" ? (
            <>
              <div className="w-7 h-7 rounded-full bg-[#3EDC81] flex items-center justify-center text-[#1A1E2A] text-xs font-bold">
                1
              </div>
              <span className="text-sm font-medium">Enter your location</span>
              <span className="mx-2 text-gray-400">→</span>
              <div className="w-7 h-7 rounded-full bg-surface border border-border flex items-center justify-center text-xs font-medium text-gray-400">
                2
              </div>
              <span className="text-sm font-medium text-gray-400">
                Add competitors
              </span>
            </>
          ) : (
            <>
              <div className="w-7 h-7 rounded-full bg-[#3EDC81] flex items-center justify-center text-[#1A1E2A] text-xs font-bold">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <span className="text-sm font-medium text-[#3EDC81]">
                Location added
              </span>
              <span className="mx-2 text-[#3EDC81]">→</span>
              <div className="w-7 h-7 rounded-full bg-[#3EDC81] flex items-center justify-center text-[#1A1E2A] text-xs font-bold">
                2
              </div>
              <span className="text-sm font-medium">AI predictions ready</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default function OnboardingLocation() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const admin = useContext(AdminContext);
  const [customerData, setCustomerData] = useState<File[]>([]);
  const [selectedBusiness, setSelectedBusiness] = useState<Partial<Location>>({
    name: "",
    description: "",
    locale: "en",
    timezone: "America/Los_Angeles",
  });
  const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);
  const [currentStep, setCurrentStep] =
    useState<OnboardingStep>("company-info");
  const [completedSteps, setCompletedSteps] = useState<Set<OnboardingStep>>(
    new Set()
  );
  const [showSkipDialog, setShowSkipDialog] = useState(false);
  const [recentlyUnlockedAgent, setRecentlyUnlockedAgent] = useState<
    string | null
  >(null);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const locationIdRef = useRef<string | null>(null); // To store the location ID after creation
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [connectedData, setConnectedData] = useState<Set<string>>(new Set());
  const [dataSources, setDataSources] = useState<Set<DataSource>>(new Set());
  const [documents, setDocuments] = useState<File[]>([]);
  const [dataCollected, setDataCollected] = useState<DataCollected>({
    manual: false,
    pos: false,
  });
  const [changeToManualUpload, setChangeToManualUpload] =
    useState<boolean>(false);
  const [showScheduleCallModal, setShowScheduleCallModal] = useState(false);
  const [subscriptionError, setSubscriptionError] = useState<string | null>(
    null
  );
  const [isIndexingProducts, setIsIndexingProducts] = useState(false);
  const [connectedSocialPlatforms, setConnectedSocialPlatforms] = useState<
    SocialPlatform[]
  >([
    {
      id: "facebook",
      name: "Facebook",
      icon: "facebook",
      connectedAccount: null,
      available: true,
      accessToken: null,
    },
    {
      id: "instagram",
      name: "Instagram",
      icon: "instagram",
      connectedAccount: null,
      available: true,
      accessToken: null,
    },
    {
      id: "linkedin",
      name: "LinkedIn",
      icon: "linkedin",
      connectedAccount: null,
      available: true,
      accessToken: null,
    },
    {
      id: "twitter",
      name: "Twitter",
      icon: "twitter",
      connectedAccount: null,
      available: false,
      accessToken: null,
    },
    {
      id: "youtube",
      name: "YouTube",
      icon: "youtube",
      connectedAccount: null,
      available: false,
      accessToken: null,
    },
    {
      id: "tiktok",
      name: "TikTok",
      icon: "tiktok",
      connectedAccount: null,
      available: false,
      accessToken: null,
    },
  ]);

  // Get the onboarding path from URL query parameters
  const location = window.location;
  const params = new URLSearchParams(location.search);
  const onboardingPath = params.get("path") || "full";
  const isQuickStart = onboardingPath === "quick";
  const [showAIToast, setShowAIToast] = useState(isQuickStart);

  // Modify steps based on onboarding path
  const steps = [
    {
      id: "company-info",
      label: t("onboarding.steps.company_info.title"),
      required: true,
    },
    {
      id: "data-sources",
      label: t("onboarding.steps.data_sources.title"),
      required: false,
      hidden: isQuickStart, // Hide this step for quick start
    },
    {
      id: "competitors",
      label: t("onboarding.steps.competitors.title"),
      required: false,
    },
    {
      id: "social-media",
      label: t("onboarding.steps.social.title"),
      required: false,
      hidden: isQuickStart, // Hide this step for quick start
    },
  ].filter((step) => !step.hidden); // Filter out hidden steps

  const loadGoogleMaps = async () => {
    // For demo purposes, we'll just set isGoogleMapsLoaded to true
    // since we're using dummy data instead of the actual Google Maps API
    setIsGoogleMapsLoaded(true);

    // Original implementation commented out for reference
    /*
    if (!window.google && !document.querySelector("#google-maps-script")) {
      try {
        let apiKey = process.env.REACT_APP_GOOGLE_MAPS_API_KEY;
        if (!apiKey) {
          apiKey = "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";
          //   throw new Error("Missing REACT_APP_GOOGLE_MAPS_API_KEY");
        }

        const script = document.createElement("script");
        script.id = "google-maps-script";
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.defer = true;
        script.onload = () => setIsGoogleMapsLoaded(true);
        script.onerror = (error) => {
          console.error("Error loading Google Maps:", error);
          setIsGoogleMapsLoaded(false);
        };

        document.head.appendChild(script);
      } catch (error) {
        console.error("Error initializing Google Maps:", error);
        setIsGoogleMapsLoaded(false);
      }
    } else if (window.google) {
      setIsGoogleMapsLoaded(true);
    }
    */
  };

  useEffect(() => {
    loadGoogleMaps();
    return () => {
      const script = document.querySelector("#google-maps-script");
      if (script) {
        script.remove();
      }
    };
  }, []);

  // Helper function to clear competitor state
  const clearCompetitorState = (reason: string) => {
    console.log(`Clearing competitor state: ${reason}`);
    setCompetitors([]);
    localStorage.removeItem("onboarding_competitors");

    // Reset competitor-related completion state
    setCompletedSteps((prev) => {
      const newSteps = new Set(prev);
      newSteps.delete("competitors");
      return newSteps;
    });
    setConnectedData((prev) => {
      const newData = new Set(prev);
      newData.delete("competitors");
      return newData;
    });
  };

  const handleBusinessSelect = async (business: Partial<Location>) => {
    try {
      if (!business.name) {
        throw new Error("Missing required location fields");
      }

      console.log("New business selected:", business.name);

      // Clear ALL previous onboarding data when selecting a new business
      localStorage.removeItem("onboarding_business_info");
      localStorage.removeItem("onboarding_competitors");
      localStorage.removeItem("last_competitor_business_id");

      // Clear previous competitor state when selecting a new business
      clearCompetitorState("new business selection");

      // Immediately update the selected business state
      setSelectedBusiness(business);

      // Store the business data in localStorage for persistence during onboarding
      try {
        const businessData = {
          name: business.name || "",
          description: business.description || "",
          address: business.address,
          city: business.city,
          state: business.state,
          zip: business.zip,
          country: business.country,
          latitude: business.latitude,
          longitude: business.longitude,
          phone: business.phone,
          website: business.website,
          locale: business.locale || "en",
          timezone: business.timezone || "America/Los_Angeles",
          onboardingPath: onboardingPath, // Store the onboarding path
          retailer_id: business.retailer_id, // Store the retailer ID if it exists
          isFromOurDatabase: business.isFromOurDatabase, // Store if from our database
        };

        localStorage.setItem(
          "onboarding_business_info",
          JSON.stringify(businessData)
        );
        console.log("Business data saved to localStorage:", businessData.name);
      } catch (error) {
        console.error("Failed to save business info to localStorage:", error);
      }

      // Mark the step as completed and move to the next step
      setCompletedSteps((prev) => new Set([...prev, "company-info"]));
      // Add company-info to connectedData for agent unlocking
      setConnectedData((prev) => new Set([...prev, "company-info"]));

      // For quick start path, simulate AI data
      if (isQuickStart) {
        // Simulate AI-powered competitor inference
        if (business.zip || business.city || business.address) {
          console.log(`Starting competitor search for business:`, {
            name: business.name,
            address: business.address,
            city: business.city,
            state: business.state,
            zip: business.zip,
            latitude: business.latitude,
            longitude: business.longitude,
          });
          getCompetitors(business);
        }
      } else {
        // Regular path - move to next step
        setCurrentStep("data-sources");
      }
    } catch (error) {
      console.error("Failed to save location:", error);
      toast.error("Failed to save location");
    }
  };

  const getCompetitors = async (business: Partial<Location>) => {
    // Show the AI toast notification immediately for a better user experience
    setShowAIToast(true);

    console.log(`🔍 Starting competitor search for business:`, {
      name: business.name,
      address: business.address,
      city: business.city,
      state: business.state,
      zip: business.zip,
      latitude: business.latitude,
      longitude: business.longitude,
    });

    let latitude = business.latitude;
    let longitude = business.longitude;

    // If coordinates are missing but we have an address, try to geocode it
    if ((!latitude || !longitude) && business.address) {
      console.log(
        "Coordinates missing, attempting to geocode business address"
      );
      toast.loading("Getting location coordinates...", {
        id: "geocoding",
      });

      try {
        // Construct a complete address string for geocoding
        const addressToGeocode = `${business.address}${
          business.city ? `, ${business.city}` : ""
        }${business.state ? `, ${business.state}` : ""}${
          business.zip ? ` ${business.zip}` : ""
        }`;

        console.log(
          `Geocoding address for competitor search: ${addressToGeocode}`
        );

        // Use the searchPlaces API to find the location
        const response = await miscService.searchPlaces(addressToGeocode);

        if (response?.suggestions && response.suggestions.length > 0) {
          // Use the first suggestion's place ID to get detailed geocoding info
          const placeId = response.suggestions[0].placePrediction.placeId;
          const geocodeResponse = await miscService.geocodePlace(placeId);

          if (geocodeResponse?.results && geocodeResponse.results.length > 0) {
            const result = geocodeResponse.results[0];

            // Extract coordinates with validation
            if (result.geometry?.location) {
              latitude = result.geometry.location.lat || 0;
              longitude = result.geometry.location.lng || 0;
            }

            // Validate coordinates
            if (
              latitude &&
              longitude &&
              validateCoordinates(latitude, longitude)
            ) {
              console.log(
                `Successfully geocoded business address: lat=${latitude}, lng=${longitude}`
              );

              // Update the business data with coordinates
              setSelectedBusiness((prev) => ({
                ...prev,
                latitude: latitude,
                longitude: longitude,
              }));

              // Also update localStorage
              const updatedBusiness = { ...business, latitude, longitude };
              localStorage.setItem(
                "onboarding_business_info",
                JSON.stringify(updatedBusiness)
              );

              toast.success("Location coordinates found!", { id: "geocoding" });
            } else {
              console.warn(
                `Invalid coordinates from geocoding: lat=${latitude}, lng=${longitude}`
              );
              latitude = undefined;
              longitude = undefined;
            }
          }
        }
      } catch (error) {
        console.error("Error geocoding business address:", error);
        toast.error("Could not get location coordinates", { id: "geocoding" });
      }
    }

    if (latitude && longitude && validateCoordinates(latitude, longitude)) {
      toast.loading("Finding competitors near your location...", {
        id: "competitor-search",
      });

      console.log(
        `🗺️ Using coordinate-based search: lat=${latitude}, lng=${longitude}`
      );

      // Call the searchNearbyRetailers function to find real competitors
      searchNearbyRetailers(
        latitude,
        longitude,
        15 // Increased radius to find more competitors
      )
        .then(async (nearbyRetailers: RetailerResult[]) => {
          // Declare dbCompetitors in the function scope
          let dbCompetitors: Competitor[] = [];
          if (nearbyRetailers.length > 0) {
            // Convert retailers to competitors
            dbCompetitors = nearbyRetailers.map((retailer: RetailerResult) => ({
              place_id: retailer.id,
              name: retailer.dispensary_name,
              address: retailer.physical_address
                ? `${retailer.physical_address}, ${retailer.city}, ${retailer.state} ${retailer.zip_code}`
                : `${retailer.city}, ${retailer.state} ${retailer.zip_code}`,
              location: {
                lat: retailer.latitude,
                lng: retailer.longitude,
              },
              distance: (retailer as any).distance || 0,
              isFromOurDatabase: true,
              productCount: retailer.productCount || 0,
            }));

            setCompetitors(dbCompetitors);
            toast.success(
              `Found ${dbCompetitors.length} competitors near you!`,
              { id: "competitor-search" }
            );
          } else {
            // No competitors found - set empty array
            setCompetitors([]);
            toast.success(
              "No competitors found in your area. You can add them manually later.",
              { id: "competitor-search" }
            );
          }

          // Mark competitors step as completed even if no competitors found
          // We still want to allow the user to proceed in quick start
          setCompletedSteps((prev) => new Set([...prev, "competitors"]));
          // Only add to connectedData if we actually found competitors
          if (nearbyRetailers.length > 0) {
            setConnectedData((prev) => new Set([...prev, "competitors"]));
          }

          // Save to localStorage - use the new competitors array, not the old state
          const competitorsToSave =
            nearbyRetailers.length > 0 ? dbCompetitors : [];
          localStorage.setItem(
            "onboarding_competitors",
            JSON.stringify(competitorsToSave)
          );

          // Don't trigger onboarding completion yet - move to competitors step
          setCurrentStep("competitors");
        })
        .catch(async (error: Error) => {
          console.error("Error finding competitors:", error);
          toast.error(
            "Couldn't find nearby competitors. You can add them manually later.",
            { id: "competitor-search" }
          );

          // Set empty array on error
          setCompetitors([]);

          // Still mark as completed so user can continue in quick start
          setCompletedSteps((prev) => new Set([...prev, "competitors"]));
          // Don't add to connectedData since no competitors were found

          // Save empty array to localStorage
          localStorage.setItem("onboarding_competitors", JSON.stringify([]));

          // Don't trigger onboarding completion yet - move to competitors step
          setCurrentStep("competitors");
        });
    } else {
      // No location coordinates - set empty array
      setCompetitors([]);
      toast.success(
        "No location coordinates available. You can add competitors manually later.",
        { id: "competitor-search" }
      );

      // Still mark as completed so user can continue in quick start
      setCompletedSteps((prev) => new Set([...prev, "competitors"]));
      // Don't add to connectedData since no competitors were found

      // Save empty array to localStorage
      localStorage.setItem("onboarding_competitors", JSON.stringify([]));

      // Don't trigger onboarding completion yet - move to competitors step
      setCurrentStep("competitors");
    }
  };

  // POS integration helper function
  const connectToPosSystem = async (
    locationId: string,
    posDataSource: DataSource
  ): Promise<PosConnectionResult> => {
    try {
      console.log(
        `Connecting to POS system: ${posDataSource.id} for location ${locationId}`
      );

      // Use the API client method instead of direct fetch
      const response = await api.pos.connectSystem(
        parseInt(locationId),
        posDataSource.id,
        {
          api_key: posDataSource.apiKey,
          site_id: posDataSource.siteId,
        }
      );

      console.log("POS connection response:", response);

      if (response.success) {
        return {
          success: true,
          productCount: response.productCount || 0,
          transactionCount: response.transactionCount || 0,
        };
      } else {
        throw new Error(response.error || "Failed to connect to POS system");
      }
    } catch (error: any) {
      console.error("Failed to connect to POS system:", error);
      return {
        success: false,
        error: error.message || "Failed to connect to POS system",
      };
    }
  };

  // Function to finalize and submit all data to the API (called at the end of onboarding)
  const finalizeOnboarding = async () => {
    try {
      // First check if the organization has available subscriptions
      if (admin?.organization_id) {
        const { canCreate, message } =
          await api.subscriptions.canCreateLocation(admin.organization_id);

        if (!canCreate) {
          toast.error(message || "Subscription limit reached", {
            id: "onboarding",
          });
          setSubscriptionError(message);
          // Redirect to pricing page
          navigate("/upgrade");
          return false;
        }
      }

      // Get the business info from localStorage
      const businessInfo = JSON.parse(
        localStorage.getItem("onboarding_business_info") || "{}"
      );

      if (!businessInfo.name) {
        throw new Error("Missing required location information");
      }

      // Convert dataSources from Set to Array and ensure it matches the expected format
      const posIntegrations = Array.from(dataSources).map((source) => ({
        name: source.name,
        id: source.id,
        available: source.available,
        connected: source.connected || false,
        apiKey: source.apiKey,
        siteId: source.siteId,
      }));

      // Create a FormData object to send to the server
      const formData = new FormData();

      // Add the invite code from session storage if it exists
      const inviteCode = sessionStorage.getItem("onboardingInviteCode");
      if (inviteCode) {
        formData.append("inviteCode", inviteCode);
        // Clear the invite code from session storage after using it
        sessionStorage.removeItem("onboardingInviteCode");
      }

      // Convert the location data to a string and add it to the form data
      const locationPayload = {
        // Basic business information
        name: businessInfo.name,
        description: businessInfo.description || "",
        locale: businessInfo.locale || "en",
        timezone: businessInfo.timezone || "America/Los_Angeles",
        address: businessInfo.address,
        city: businessInfo.city,
        state: businessInfo.state,
        zip: businessInfo.zip,
        country: businessInfo.country,
        phone: businessInfo.phone || "",
        website: businessInfo.website || "",
        latitude: Number(businessInfo.latitude || 0),
        longitude: Number(businessInfo.longitude || 0),
        // Store as posIntegrations in the data field for better semantics
        data: {
          posIntegrations: posIntegrations,
          isFromOurDatabase: businessInfo.isFromOurDatabase, // Store whether this came from our DB
        },
        // Social media links (mapped from social platforms if applicable)
        facebook:
          connectedSocialPlatforms.find((p) => p.id === "facebook")
            ?.connectedAccount || businessInfo.facebook,
        twitter:
          connectedSocialPlatforms.find((p) => p.id === "twitter")
            ?.connectedAccount || businessInfo.twitter,
        instagram:
          connectedSocialPlatforms.find((p) => p.id === "instagram")
            ?.connectedAccount || businessInfo.instagram,
        linkedin:
          connectedSocialPlatforms.find((p) => p.id === "linkedin")
            ?.connectedAccount || businessInfo.linkedin,

        // Default settings
        link_wrap_email: false,
        link_wrap_push: false,

        // Add retailer_id if it exists
        retailer_id: businessInfo.retailer_id,

        // Add Green Check CRB ID if using Green Check
        pos_location_id:
          Array.from(dataSources).find((ds) => ds.id === "greencheck")?.crbId ||
          Array.from(dataSources).find((ds) => ds.id === "greencheck")
            ?.apiKey ||
          null,

        // Add competitors array to be saved to location_competitors table
        competitors: competitors.length > 0 ? competitors : [],
      };

      // Convert the location data to a string and add it to the form data
      formData.append("locationData", JSON.stringify(locationPayload));

      // Add POS integration data if available
      if (dataCollected.pos && dataSources.size > 0) {
        const posDataSource = Array.from(dataSources).find(
          (ds) =>
            ds.id === "marijuanasoftware" ||
            ds.id === "pos" ||
            ds.id === "greencheck"
        );

        if (posDataSource && (posDataSource.apiKey || posDataSource.crbId)) {
          // First try to test the connection and validate credentials
          if (!locationIdRef.current) {
            console.log(
              "Creating POS integration configuration to add after location creation"
            );

            // Handle Green Check differently
            if (posDataSource.id === "greencheck") {
              const posConfig = {
                type: "greencheck",
                config: {
                  crb_id: posDataSource.crbId || posDataSource.apiKey,
                  service_provider_id: "f2b48e57-7114-432d-8f11-a0d74b8fb934",
                },
              };
              formData.append("posIntegration", JSON.stringify(posConfig));
              console.log(
                "Added Green Check POS integration config to FormData"
              );
            } else {
              const posConfig = {
                type: posDataSource.id,
                config: {
                  api_key: posDataSource.apiKey,
                  site_id: posDataSource.siteId,
                },
              };
              formData.append("posIntegration", JSON.stringify(posConfig));
              console.log("Added POS integration config to FormData");
            }
          } else {
            // If we already have a location ID, connect to POS system immediately
            console.log("Testing POS connection before finalizing");
            try {
              const posResult = await connectToPosSystem(
                locationIdRef.current,
                posDataSource
              );

              if (posResult.success) {
                console.log("POS connection successful:", posResult);
                toast.success("Connected to POS system successfully!", {
                  id: "pos-connection",
                  duration: 3000,
                });

                // If POS connection was successful and imported products, trigger indexing
                if (posResult.productCount && posResult.productCount > 0) {
                  console.log(
                    `POS imported ${posResult.productCount} products, triggering indexing...`
                  );
                  setIsIndexingProducts(true);
                  try {
                    await api.products.reVectorize(
                      parseInt(locationIdRef.current),
                      {
                        batch_size: 100,
                      }
                    );
                    console.log(
                      "Product indexing triggered after POS connection"
                    );
                    toast.success(
                      `${posResult.productCount} products imported and indexing for AI analysis...`,
                      {
                        id: "pos-indexing",
                        duration: 5000,
                      }
                    );
                  } catch (indexError) {
                    console.warn(
                      "Failed to trigger product indexing after POS connection:",
                      indexError
                    );
                    toast.success(
                      "Products imported successfully. Indexing will happen in background.",
                      {
                        id: "pos-indexing",
                        duration: 4000,
                      }
                    );
                  } finally {
                    setIsIndexingProducts(false);
                  }
                }
              } else {
                console.warn("POS connection issue:", posResult.error);
                toast.error(`POS connection issue: ${posResult.error}`, {
                  id: "pos-connection",
                  duration: 5000,
                });
              }
            } catch (posError) {
              console.error("Error connecting to POS:", posError);
              // We'll continue with onboarding even if POS connection fails
            }
          }
        }
      }

      // Add POS data files if any were uploaded manually
      if (customerData.length > 0) {
        console.log(`Adding ${customerData.length} POS data files to FormData`);
        customerData.forEach((file, index) => {
          formData.append(`posDataFile_${index}`, file);
          console.log(`Added POS data file to FormData: ${file.name}`);
        });
      }

      // Add document files if any were uploaded
      if (documents.length > 0) {
        console.log(`Adding ${documents.length} document files to FormData`);
        documents.forEach((file, index) => {
          formData.append(`document_${index}`, file);
          console.log(`Added document file to FormData: ${file.name}`);
        });
      }

      // Create or update the location with all data in one request
      let endpoint;
      let method;

      if (locationIdRef.current) {
        // Update existing location
        endpoint = `/api/admin/locations/${locationIdRef.current}/onboard`;
        method = "PUT";
      } else {
        // Create a new location
        endpoint = "/api/admin/locations/onboard";
        method = "POST";
      }

      console.log(`Submitting FormData to ${endpoint} via ${method}`);

      try {
        // Log the FormData entries to verify content
        console.log("FormData entries:");
        for (const pair of formData.entries()) {
          console.log(
            `${pair[0]}: ${
              typeof pair[1] === "object"
                ? "File: " + (pair[1] as File).name
                : pair[1]
            }`
          );
        }

        const response = await fetch(endpoint, {
          method,
          body: formData,
        }).then((res) => res.json());

        console.log("Response received:", response);

        if (!response.success) {
          throw new Error(response.error || "Failed to complete onboarding");
        }

        // Store the location ID reference
        const locationId = response.location.id.toString();
        locationIdRef.current = locationId;

        // Store the location ID in recent locations
        pushRecentLocation(response.location.id);

        // Check if we imported products and need to index them
        const hasProductData =
          customerData.length > 0 || documents.length > 0 || dataCollected.pos;

        if (hasProductData) {
          console.log(
            "Product data was imported, initiating product indexing..."
          );
          setIsIndexingProducts(true);

          try {
            // Trigger product vectorization/indexing after successful import
            await api.products.reVectorize(parseInt(locationId), {
              batch_size: 100,
            });
            console.log("Product indexing initiated successfully");

            toast.success(
              "Business profile created! Product data is being indexed for AI analysis...",
              {
                id: "onboarding",
                duration: 7000,
              }
            );
          } catch (indexError) {
            console.warn("Failed to trigger product indexing:", indexError);
            // Don't fail the entire onboarding process if indexing fails
            toast.success(
              "Business profile created! Product indexing will be done in background.",
              {
                id: "onboarding",
                duration: 5000,
              }
            );
          } finally {
            setIsIndexingProducts(false);
          }
        } else {
          // Check if processing is still ongoing in the background
          if (response.status === "processing") {
            toast.success(
              "Business profile created! Processing data in background...",
              {
                id: "onboarding",
                duration: 5000,
              }
            );
          } else {
            toast.success("Business profile created successfully!", {
              id: "onboarding",
            });
          }
        }

        return true;
      } catch (error) {
        console.error("Failed to finalize onboarding:", error);
        toast.error("Failed to complete onboarding", { id: "onboarding" });
        return false;
      }
    } catch (error) {
      console.error("Failed to finalize onboarding:", error);
      toast.error("Failed to complete onboarding", { id: "onboarding" });
      return false;
    }
  };

  const handleStepComplete = (step: OnboardingStep) => {
    setCompletedSteps((prev) => new Set([...prev, step]));

    const currentIndex = steps.findIndex((s) => s.id === step);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].id as OnboardingStep);
    } else {
      navigate(`/locations/${locationIdRef.current}/chat`);
    }
  };

  const handleBack = () => {
    const currentIndex = steps.findIndex((s) => s.id === currentStep);
    if (currentIndex > 0) {
      // If we're going back from competitors step, clear competitor state
      if (currentStep === "competitors") {
        clearCompetitorState("going back from competitors step");
      }

      setCurrentStep(steps[currentIndex - 1].id as OnboardingStep);
    }
  };

  const handleDataSourceConnect = async (
    type: "pos" | "customer_data",
    dataSource: DataSource
  ) => {
    // Check if this is a disconnect operation
    if (dataSource.connected === false) {
      // Remove the data source from connected data
      setConnectedData((prev) => {
        const newData = new Set(prev);
        newData.delete(type);
        return newData;
      });

      // Remove the data source from tracked sources
      setDataSources((prev) => {
        const newSources = new Set(prev);
        const existingSource = Array.from(newSources).find(
          (s) => s.id === dataSource.id
        );
        if (existingSource) {
          newSources.delete(existingSource);
        }
        return newSources;
      });

      return;
    }

    // Add the data source to connected data
    setConnectedData((prev) => new Set([...prev, type]));

    // Add the data source to our tracked sources
    setDataSources((prev) => new Set([...prev, dataSource]));

    // If we already have a location ID and this is a POS connection, test it immediately
    if (type === "pos" && locationIdRef.current && dataSource.apiKey) {
      try {
        toast.loading("Testing POS connection...", { id: "pos-test" });

        // Test the connection
        const posResult = await connectToPosSystem(
          locationIdRef.current,
          dataSource
        );

        if (posResult.success) {
          toast.success("Connected to POS system successfully!", {
            id: "pos-test",
            duration: 3000,
          });

          // Mark the data source as connected
          const updatedSource = { ...dataSource, connected: true };
          setDataSources((prev) => {
            const newSources = new Set(prev);
            newSources.delete(dataSource);
            newSources.add(updatedSource);
            return newSources;
          });
        } else {
          toast.error(`POS connection issue: ${posResult.error}`, {
            id: "pos-test",
            duration: 5000,
          });
        }
      } catch (error: any) {
        console.error("Error testing POS connection:", error);
        toast.error(`Failed to test POS connection: ${error.message}`, {
          id: "pos-test",
          duration: 5000,
        });
      }
    }

    if (!completedSteps.has("data-sources")) {
      setCompletedSteps((prev) => new Set([...prev, "data-sources"]));
      // Add data-sources to connectedData when any data source is connected
      setConnectedData((prev) => new Set([...prev, "data-sources"]));
    }
  };

  // Handle business documents status changes
  const handleBusinessDocumentsStatusChange = (hasDocuments: boolean) => {
    if (hasDocuments) {
      // Mark business-documents as completed when documents are uploaded
      setCompletedSteps((prev) => new Set([...prev, "business-documents"]));
      // Add to connectedData for agent unlocking
      setConnectedData((prev) => new Set([...prev, "business-documents"]));
    } else {
      // Remove business-documents when all documents are removed
      setCompletedSteps((prev) => {
        const newSteps = new Set(prev);
        newSteps.delete("business-documents");
        return newSteps;
      });
      // Remove from connectedData for agent unlocking
      setConnectedData((prev) => {
        const newData = new Set(prev);
        newData.delete("business-documents");
        return newData;
      });
    }
  };

  // Handle competitors status changes, similar to business documents
  const handleCompetitorsChange = (competitors: Competitor[]) => {
    // Update completedSteps and connectedData based on competitors
    if (competitors.length > 0) {
      // Mark competitors step as completed when competitors are added
      setCompletedSteps((prev) => new Set([...prev, "competitors"]));
      // Add to connectedData for agent unlocking (specifically for EZAL)
      setConnectedData((prev) => new Set([...prev, "competitors"]));
    } else {
      // Remove competitors step when all competitors are removed
      setCompletedSteps((prev) => {
        const newSteps = new Set(prev);
        newSteps.delete("competitors");
        return newSteps;
      });
      // Remove from connectedData for agent unlocking
      setConnectedData((prev) => {
        const newData = new Set(prev);
        newData.delete("competitors");
        return newData;
      });
    }
  };

  // Handle social media connections
  const handleSocialMediaChange = (platforms: SocialPlatform[]) => {
    // Check if any platforms are connected
    const hasConnections = platforms.some((p) => p.connectedAccount);

    if (hasConnections) {
      // Mark social-media step as completed when any platform is connected
      setCompletedSteps((prev) => new Set([...prev, "social-media"]));
      // Add to connectedData for agent unlocking
      setConnectedData((prev) => new Set([...prev, "social-media"]));
    } else {
      // Remove social-media when all connections are removed
      setCompletedSteps((prev) => {
        const newSteps = new Set(prev);
        newSteps.delete("social-media");
        return newSteps;
      });
      // Remove from connectedData for agent unlocking
      setConnectedData((prev) => {
        const newData = new Set(prev);
        newData.delete("social-media");
        return newData;
      });
    }
  };

  const canSkip = connectedData.size > 0 && completedSteps.has("company-info");

  useEffect(() => {
    const getAppCheckToken = async () => {
      if (!appCheck) {
        console.warn("App Check not initialized");
        return null;
      }

      let retries = 3;
      while (retries > 0) {
        try {
          console.log("Getting App Check token...");
          const token = await getToken(
            appCheck,
            /* forceRefresh */ retries === 1
          );
          console.log("App Check token retrieved successfully");
          return token;
        } catch (error) {
          console.error(
            `Failed to get App Check token (${retries} retries left):`,
            error
          );
          retries--;
          if (retries === 0) return null;
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
      return null;
    };

    getAppCheckToken();
  }, []);

  // Load saved business info from localStorage only on initial load
  useEffect(() => {
    // Only load from localStorage if we don't already have a selected business
    if (selectedBusiness.name) {
      console.log("Business already selected, skipping localStorage load");
      return;
    }

    try {
      const savedBusinessInfo = localStorage.getItem(
        "onboarding_business_info"
      );
      if (savedBusinessInfo) {
        const parsedInfo = JSON.parse(savedBusinessInfo);
        console.log("Loading business from localStorage:", parsedInfo.name);
        setSelectedBusiness((prev) => ({
          ...prev,
          ...parsedInfo, // Merge all saved properties
        }));

        // If we previously completed this step, mark it
        if (parsedInfo.name) {
          setCompletedSteps((prev) => new Set([...prev, "company-info"]));
          setConnectedData((prev) => new Set([...prev, "company-info"]));
        }
      }

      // Check for saved competitors - only restore if we have a matching business
      const savedCompetitors = localStorage.getItem("onboarding_competitors");
      const savedBusinessForCompetitors = localStorage.getItem(
        "onboarding_business_info"
      );

      if (savedCompetitors && savedBusinessForCompetitors) {
        try {
          const parsedCompetitors = JSON.parse(savedCompetitors);
          const parsedBusiness = JSON.parse(savedBusinessForCompetitors);

          // Only restore competitors if we have a business with coordinates
          // This prevents restoring stale competitors from different locations
          if (
            Array.isArray(parsedCompetitors) &&
            parsedCompetitors.length > 0 &&
            parsedBusiness.latitude &&
            parsedBusiness.longitude &&
            validateCoordinates(
              parsedBusiness.latitude,
              parsedBusiness.longitude
            )
          ) {
            console.log(
              `Restoring ${parsedCompetitors.length} competitors for business: ${parsedBusiness.name}`
            );
            setCompetitors(parsedCompetitors);
            // Make sure competitors step is properly marked
            setCompletedSteps((prev) => new Set([...prev, "competitors"]));
            setConnectedData((prev) => new Set([...prev, "competitors"]));
          } else {
            console.log(
              "Not restoring competitors - invalid business coordinates or no competitors"
            );
            // Clear stale competitor data
            localStorage.removeItem("onboarding_competitors");
          }
        } catch (error) {
          console.error(
            "Failed to parse saved competitors or business info:",
            error
          );
          // Clear corrupted data
          localStorage.removeItem("onboarding_competitors");
        }
      } else if (savedCompetitors && !savedBusinessForCompetitors) {
        console.log(
          "Found competitors but no business info - clearing stale competitor data"
        );
        localStorage.removeItem("onboarding_competitors");
      }

      // Check for saved social media connections
      const savedSocialMedia = localStorage.getItem("onboarding_social_media");
      if (savedSocialMedia) {
        try {
          const parsedPlatforms = JSON.parse(savedSocialMedia);
          if (Array.isArray(parsedPlatforms)) {
            // Check if any platforms are connected
            const hasConnections = parsedPlatforms.some(
              (p) => p.connectedAccount
            );
            if (hasConnections) {
              setConnectedSocialPlatforms(parsedPlatforms);
              // Mark social-media as completed
              setCompletedSteps((prev) => new Set([...prev, "social-media"]));
              setConnectedData((prev) => new Set([...prev, "social-media"]));
            }
          }
        } catch (error) {
          console.error("Failed to parse saved social media platforms:", error);
        }
      }
    } catch (error) {
      console.error("Failed to load business info from localStorage:", error);
    }
  }, []); // Only run once on mount

  // Add function to handle step click
  const handleStepClick = (stepId: string) => {
    // Only allow navigation to completed steps or the current step
    if (
      completedSteps.has(stepId as OnboardingStep) ||
      stepId === currentStep
    ) {
      // If we're navigating away from competitors step, clear competitor state
      if (currentStep === "competitors" && stepId !== "competitors") {
        clearCompetitorState(
          `navigating away from competitors step to ${stepId}`
        );
      }

      setCurrentStep(stepId as OnboardingStep);
    }
  };

  // Handle social media platforms with finalization
  const handleSocialMediaComplete = async () => {
    const success = await finalizeOnboarding();

    if (success) {
      // Mark the step as completed
      setCompletedSteps((prev) => new Set([...prev, "social-media"]));

      // Clear all localStorage for onboarding
      localStorage.removeItem("onboarding_business_info");
      localStorage.removeItem("onboarding_communication");
      localStorage.removeItem("onboarding_competitors");
      localStorage.removeItem("onboarding_documents");
      localStorage.removeItem("onboarding_social_platforms");
      localStorage.removeItem("onboarding_data_sources");
      localStorage.removeItem("onboarding_social_media");

      // Show the schedule call modal instead of navigating immediately
      setShowScheduleCallModal(true);
    }
  };

  // Handle navigation to dashboard after scheduling or skipping the call
  const handleScheduleCallComplete = () => {
    // Navigate to the chat page
    navigate(`/locations/${locationIdRef.current}/chat`);
  };

  return (
    <div className="onboarding bg-surface  min-h-screen">
      {/* Header */}
      <div className="max-w-7xl mx-auto pt-3 px-3 sm:px-4">
        <p className="text-center text-gray-400 mb-4 sm:mb-6">
          {isQuickStart
            ? "Smokey's AI predicts what sells in your area — no data required"
            : "Unlock powerful AI agents by connecting your data"}
        </p>

        {/* Product Indexing Status Indicator */}
        {isIndexingProducts && (
          <div className="bg-[#3EDC81]/10 border border-[#3EDC81]/20 rounded-lg p-3 mb-4 mx-auto max-w-lg">
            <div className="flex items-center justify-center space-x-3">
              <div className="w-5 h-5 border-2 border-[#3EDC81] border-t-transparent rounded-full animate-spin"></div>
              <span className="text-[#3EDC81] font-medium">
                Indexing products for AI analysis...
              </span>
            </div>
            <p className="text-center text-gray-400 text-sm mt-2">
              This will enable your AI agents to understand your product catalog
            </p>
          </div>
        )}

        {/* Main Content Container */}
        <div className="flex flex-col lg:flex-row gap-3 xl:gap-4">
          {/* Left Column (68%) - Main onboarding steps */}
          <div className="lg:w-[68%] w-full">
            {/* Setup Progress */}
            {isQuickStart ? (
              <QuickStartProgress currentStep={currentStep} />
            ) : (
              <div className="mb-3 sm:mb-4">
                <StepProgress
                  steps={steps}
                  currentStep={currentStep}
                  completedSteps={completedSteps}
                  onStepClick={handleStepClick}
                />
              </div>
            )}

            {/* Current Step Content */}
            <div className="bg-surface  rounded-lg p-3 md:p-4 shadow-md transition-all">
              <div className="w-full">
                {currentStep === "company-info" && (
                  <CompanyInfoStep
                    selectedBusiness={selectedBusiness}
                    onBusinessSelect={handleBusinessSelect}
                    onStepComplete={() => handleStepComplete("company-info")}
                    isCompleted={completedSteps.has("company-info")}
                    isGoogleMapsLoaded={isGoogleMapsLoaded}
                    showManualEntry={showManualEntry}
                    setShowManualEntry={setShowManualEntry}
                  />
                )}

                {currentStep === "data-sources" && selectedBusiness && (
                  <DataSourcesStep
                    setDataCollected={setDataCollected}
                    dataCollected={dataCollected}
                    changeToManualUpload={changeToManualUpload}
                    setChangeToManualUpload={setChangeToManualUpload}
                    customerData={customerData}
                    setCustomerData={setCustomerData}
                    dataSources={dataSources}
                    setDataSources={setDataSources}
                    selectedBusiness={selectedBusiness}
                    setDocuments={setDocuments}
                    documents={documents}
                    onDataSourceConnect={handleDataSourceConnect}
                    onStepComplete={() => handleStepComplete("data-sources")}
                    canSkip={canSkip}
                    isCompleted={completedSteps.has("data-sources")}
                    onBack={handleBack}
                    onBusinessDocumentsUploaded={
                      handleBusinessDocumentsStatusChange
                    }
                  />
                )}

                {currentStep === "competitors" && (
                  <CompetitorsStep
                    selectedBusiness={selectedBusiness}
                    onStepComplete={() => handleStepComplete("competitors")}
                    isCompleted={completedSteps.has("competitors")}
                    onBack={handleBack}
                    competitors={competitors}
                    setCompetitors={(newCompetitors) => {
                      setCompetitors(newCompetitors);
                      handleCompetitorsChange(newCompetitors);
                    }}
                    onFinalizeOnboarding={
                      isQuickStart
                        ? async () => {
                            const success = await finalizeOnboarding();
                            if (success) {
                              // Clear localStorage
                              localStorage.removeItem(
                                "onboarding_business_info"
                              );
                              localStorage.removeItem(
                                "onboarding_communication"
                              );
                              localStorage.removeItem("onboarding_competitors");
                              localStorage.removeItem("onboarding_documents");
                              localStorage.removeItem(
                                "onboarding_social_platforms"
                              );
                              localStorage.removeItem(
                                "onboarding_data_sources"
                              );
                              localStorage.removeItem(
                                "onboarding_social_media"
                              );

                              // Show the schedule call modal
                              setShowScheduleCallModal(true);
                            }
                            return success;
                          }
                        : undefined
                    }
                  />
                )}

                {currentStep === "social-media" && (
                  <SocialMediaStep
                    onStepComplete={handleSocialMediaComplete}
                    isCompleted={completedSteps.has("social-media")}
                    onBack={handleBack}
                    connectedSocialPlatforms={connectedSocialPlatforms}
                    setConnectedSocialPlatforms={(platforms) => {
                      setConnectedSocialPlatforms(platforms);
                      handleSocialMediaChange(platforms);
                    }}
                    finalStep={true}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Right Column (32%) - AI Agents */}
          <div className="lg:w-[32%] w-full">
            <div className="sticky top-4">
              <div className="flex items-center mb-3 min-w-[328px]">
                <div className="mr-2 ">🤖</div>
                <h3 className="text-lg font-medium ">
                  Unlockable AI Agents{" "}
                  <span className="bg-[#3EDC81] text-[#1A1E2A] text-xs py-0.5 px-2 rounded-full ml-2">
                    {agents.filter((a) => !a.disabled).length}
                  </span>
                </h3>
              </div>

              {/* Sell More Category */}
              <div className="mb-3">
                <div className="bg-surface  rounded-lg overflow-hidden">
                  <button
                    className="flex items-center justify-between w-full p-3 text-left font-medium"
                    onClick={() => {
                      const element =
                        document.getElementById("sell-more-agents");
                      if (element) element.classList.toggle("hidden");
                    }}
                  >
                    <div className="flex items-center">
                      <span className="mr-2">💰</span>
                      <span>Sell More</span>
                    </div>
                    <svg
                      className="w-4 h-4 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>
                  <div
                    id="sell-more-agents"
                    className="border-t border-[#313642] p-2 space-y-2"
                  >
                    {agents
                      .filter((agent) =>
                        ["smokey", "craig", "pops"].includes(agent.id)
                      )
                      .map((agent) => {
                        let microTip = "";
                        if (agent.id === "smokey")
                          microTip =
                            "AI recommendations—raise ticket size by 30%";
                        if (agent.id === "craig")
                          microTip = "Effortless marketing campaigns";
                        if (agent.id === "pops")
                          microTip = "Boost operational efficiency";

                        return (
                          <div key={agent.id} className="relative">
                            <CompactAgentCard
                              agent={{
                                ...agent,
                                description: microTip || agent.description,
                              }}
                              completedSteps={completedSteps}
                              connectedData={connectedData}
                              recentlyUnlockedAgent={recentlyUnlockedAgent}
                              setRecentlyUnlockedAgent={
                                setRecentlyUnlockedAgent
                              }
                            />
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>

              {/* Insights Category */}
              <div className="mb-3">
                <div className="bg-surface  rounded-lg overflow-hidden">
                  <button
                    className="flex items-center justify-between w-full p-3 text-left font-medium"
                    onClick={() => {
                      const element =
                        document.getElementById("insights-agents");
                      if (element) element.classList.toggle("hidden");
                    }}
                  >
                    <div className="flex items-center">
                      <span className="mr-2">📊</span>
                      <span>Insights</span>
                    </div>
                    <svg
                      className="w-4 h-4 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>
                  <div
                    id="insights-agents"
                    className="border-t border-[#313642] p-2 space-y-2"
                  >
                    {agents
                      .filter((agent) =>
                        ["money-mike", "ezal", "mrs-parker"].includes(agent.id)
                      )
                      .map((agent) => {
                        let microTip = "";
                        if (agent.id === "money-mike")
                          microTip = "Financial analysis";
                        if (agent.id === "ezal")
                          microTip = "Market intelligence";
                        if (agent.id === "mrs-parker")
                          microTip = "Customer insights";

                        return (
                          <div key={agent.id} className="relative">
                            <CompactAgentCard
                              agent={{
                                ...agent,
                                description: microTip || agent.description,
                              }}
                              completedSteps={completedSteps}
                              connectedData={connectedData}
                              recentlyUnlockedAgent={recentlyUnlockedAgent}
                              setRecentlyUnlockedAgent={
                                setRecentlyUnlockedAgent
                              }
                            />
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>

              {/* Stay Legal Category */}
              <div className="mb-3">
                <div className="bg-surface  rounded-lg overflow-hidden">
                  <button
                    className="flex items-center justify-between w-full p-3 text-left font-medium"
                    onClick={() => {
                      const element = document.getElementById("legal-agents");
                      if (element) element.classList.toggle("hidden");
                    }}
                  >
                    <div className="flex items-center">
                      <span className="mr-2">🛡️</span>
                      <span>Stay Legal</span>
                    </div>
                    <svg
                      className="w-4 h-4 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>
                  <div
                    id="legal-agents"
                    className="border-t border-[#313642] p-2 space-y-2"
                  >
                    {agents
                      .filter((agent) => ["deebo"].includes(agent.id))
                      .map((agent) => (
                        <div key={agent.id} className="relative">
                          <CompactAgentCard
                            agent={{
                              ...agent,
                              description: "Compliance monitoring",
                            }}
                            completedSteps={completedSteps}
                            connectedData={connectedData}
                            recentlyUnlockedAgent={recentlyUnlockedAgent}
                            setRecentlyUnlockedAgent={setRecentlyUnlockedAgent}
                          />
                        </div>
                      ))}
                  </div>
                </div>
              </div>

              {/* Coming Soon Category */}
              <div className="mb-3 opacity-70">
                <div className="bg-surface rounded-lg overflow-hidden">
                  <button
                    className="flex items-center justify-between w-full p-3 text-left font-medium"
                    onClick={() => {
                      const element =
                        document.getElementById("coming-soon-agents");
                      if (element) element.classList.toggle("hidden");
                    }}
                  >
                    <div className="flex items-center">
                      <span className="mr-2">🔜</span>
                      <span>Coming Soon</span>
                    </div>
                    <svg
                      className="w-4 h-4 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>
                  <div
                    id="coming-soon-agents"
                    className="border-t border-[#313642] p-2 space-y-2 hidden"
                  >
                    {agents
                      .filter((agent) =>
                        ["day-day", "big-worm"].includes(agent.id)
                      )
                      .map((agent) => (
                        <div key={agent.id} className="relative">
                          <CompactAgentCard
                            agent={agent}
                            completedSteps={completedSteps}
                            connectedData={connectedData}
                            recentlyUnlockedAgent={recentlyUnlockedAgent}
                            setRecentlyUnlockedAgent={setRecentlyUnlockedAgent}
                          />
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dialogs */}
      <SkipDialog
        open={showSkipDialog}
        onOpenChange={setShowSkipDialog}
        onConfirm={() => {
          setShowSkipDialog(false);
          navigate(`/locations/${locationIdRef.current}/chat`);
        }}
      />

      {/* AI Prediction Toast */}
      {showAIToast && (
        <div className="fixed bottom-0 inset-x-0 mx-auto max-w-xl z-50">
          <AIPredictionToast
            locationName={selectedBusiness?.name}
            city={selectedBusiness?.city}
            state={selectedBusiness?.state}
            zip={selectedBusiness?.zip}
            latitude={selectedBusiness?.latitude}
            longitude={selectedBusiness?.longitude}
            onClose={() => setShowAIToast(false)}
          />
        </div>
      )}

      {/* Schedule Call Modal */}
      {showScheduleCallModal && (
        <ScheduleCallModal
          businessName={selectedBusiness?.name}
          onClose={() => setShowScheduleCallModal(false)}
          onScheduleCall={handleScheduleCallComplete}
          onSkip={handleScheduleCallComplete}
        />
      )}
    </div>
  );
}
