export interface POSData {
  id: number;
  location_id: number;
  location_name: string;
  master_category: string;
  order_date: string;
  customer_type: string;
  budtender_name: string;
  gross_sales: number;
  returned_amount: number;
  discounted_amount: number;
  loyalty_as_discount: number;
  net_sales: number;
  inventory_cost: number;
  inventory_profit: number;
  loyalty_as_payment: number;
  tax_amount: number;
  invoice_total: number;
  amount_paid_in_cash: number;
  amount_paid_in_debit: number;
  birth_date?: string;
  customer_name: string;
  product_name: string;
  // New fields
  wholesale_cost?: number;
  profit_margin?: number;
  customer_email?: string;
  customer_phone?: string;
  customer_id?: string;
  order_id?: number;
}
