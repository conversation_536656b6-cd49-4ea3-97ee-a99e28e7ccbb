export interface SearchParams {
  cursor?: string;
  page?: "prev" | "next";
  limit?: number;
  sort?: string;
  direction?: "asc" | "desc";
  query?: string;
  filters?: Record<string, any>;
}

export interface SearchResult<T> {
  results: T[];
  nextCursor?: string;
  prevCursor?: string;
  limit: number;
}

export interface POSData {
  id: number;
  location_id: number;
  location_name: string;
  master_category: string;
  order_date: string;
  customer_type: string;
  budtender_name: string;
  gross_sales: number;
  returned_amount: number;
  discounted_amount: number;
  loyalty_as_discount: number;
  net_sales: number;
  inventory_cost: number;
  inventory_profit: number;
  loyalty_as_payment: number;
  tax_amount: number;
  invoice_total: number;
  amount_paid_in_cash: number;
  amount_paid_in_debit: number;
  birth_date?: string;
  customer_name: string;
  product_name: string;
  // New fields
  wholesale_cost?: number;
  profit_margin?: number;
  customer_email?: string;
  customer_phone?: string;
  customer_id?: string;
  order_id?: number;
}

export interface ProductParams {
  location_id: number;
  meta_sku: string;
  retailer_id: string;
  cann_sku_id?: string;
  brand_name?: string;
  brand_id?: number;
  url?: string;
  image_url?: string;
  new_image_file?: File;
  raw_product_name: string;
  product_name: string;
  raw_weight_string?: string;
  display_weight?: string;
  raw_product_category?: string;
  category?: string;
  raw_subcategory?: string;
  subcategory?: string;
  product_tags?: string[];
  percentage_thc?: number | null;
  percentage_cbd?: number | null;
  mg_thc?: number | null;
  mg_cbd?: number | null;
  quantity_per_package?: number | null;
  medical?: boolean;
  recreational?: boolean;
  latest_price?: number | null;
  menu_provider?: string;
  product_description?: string;
  short_description?: string;
  effects?: string | string[];
  mood?: string | string[];
  data?: any;
  ai_enhanced_fields?: string[];
  inventory_quantity?: number;
  out_of_stock?: boolean;
  slug?: string;
  is_active?: boolean;
}
