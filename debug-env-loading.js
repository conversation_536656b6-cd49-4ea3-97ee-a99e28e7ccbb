/**
 * Debug script to check environment file loading
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging Environment File Loading...\n');

// Check if files exist
const rootEnvPath = '.env';
const platformEnvPath = 'apps/platform/.env';

console.log('📁 File Existence Check:');
console.log(`   Root .env: ${fs.existsSync(rootEnvPath) ? '✅ Exists' : '❌ Missing'}`);
console.log(`   Platform .env: ${fs.existsSync(platformEnvPath) ? '✅ Exists' : '❌ Missing'}`);

// Check file contents for Green Check variables
if (fs.existsSync(rootEnvPath)) {
  const rootContent = fs.readFileSync(rootEnvPath, 'utf8');
  const hasGreenCheck = rootContent.includes('GREEN_CHECK_CLIENT_ID');
  console.log(`   Root .env has Green Check vars: ${hasGreenCheck ? '✅ Yes' : '❌ No'}`);
}

if (fs.existsSync(platformEnvPath)) {
  const platformContent = fs.readFileSync(platformEnvPath, 'utf8');
  const hasGreenCheck = platformContent.includes('GREEN_CHECK_CLIENT_ID');
  console.log(`   Platform .env has Green Check vars: ${hasGreenCheck ? '✅ Yes' : '❌ No'}`);
}

// Check current working directory
console.log(`\n📂 Current Working Directory: ${process.cwd()}`);

// Check environment variables
console.log('\n🔧 Environment Variables:');
const greenCheckVars = [
  'GREEN_CHECK_CLIENT_ID',
  'GREEN_CHECK_CLIENT_SECRET', 
  'GREEN_CHECK_SERVICE_PROVIDER_ID',
  'GREEN_CHECK_BASE_URL',
  'GREEN_CHECK_ENVIRONMENT'
];

greenCheckVars.forEach(varName => {
  const value = process.env[varName];
  console.log(`   ${varName}: ${value ? '✅ Set' : '❌ Missing'}`);
  if (value && varName !== 'GREEN_CHECK_CLIENT_SECRET') {
    console.log(`      Value: ${value}`);
  }
});

// Check if dotenv can load the files manually
console.log('\n🔄 Manual dotenv loading test:');
try {
  require('dotenv').config({ path: '.env' });
  console.log('   ✅ Root .env loaded successfully');
} catch (error) {
  console.log(`   ❌ Root .env loading failed: ${error.message}`);
}

try {
  require('dotenv').config({ path: 'apps/platform/.env' });
  console.log('   ✅ Platform .env loaded successfully');
} catch (error) {
  console.log(`   ❌ Platform .env loading failed: ${error.message}`);
}

// Check variables after manual loading
console.log('\n🔍 After manual loading:');
greenCheckVars.forEach(varName => {
  const value = process.env[varName];
  console.log(`   ${varName}: ${value ? '✅ Set' : '❌ Missing'}`);
});
